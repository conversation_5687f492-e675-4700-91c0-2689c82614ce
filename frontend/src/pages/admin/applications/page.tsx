import React, { useState, useEffect, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { useIntl } from 'react-intl';
import {
  Search,
  Filter,
  MoreVertical,
  Eye,
  Edit,
  CheckCircle,
  XCircle,
  Power,
  PowerOff,
  BarChart3,
  Users,
  Calendar,
  Activity,
  AlertTriangle,
  Shield,
  Settings,
  RefreshCw,
  Download,
  Trash2,
  Clock,
  Globe,
  Key
} from 'lucide-react';

import { PageLoader } from '@/components/base/page-loader';
import FilterPanel from '@/components/common/filter-panel';
import { ApplicationDetailsDialog } from '@/components/features/admin/application-details-dialog';
import {
  getApplicationFilterCategories,
  filterApplications
} from '@/data/admin-filters';
import { approveAdminApplication, rejectAdminApplication, suspendAdminApplication, reactivateAdminApplication } from '@/services/admin-api';

interface AdminApplication {
  id: string;
  name: string;
  clientId: string;
  description: string;
  redirectUris: string[];
  scopes: string[];
  status: 'pending' | 'approved' | 'rejected' | 'suspended';
  isActive: boolean;
  isApproved: boolean;
  createdAt: string;
  updatedAt: string;
  developer: {
    id: string;
    username: string;
    email: string;
  };
  usage: {
    totalUsers: number;
    activeUsers: number;
    requestsToday: number;
    requestsThisMonth: number;
  };
  // Legacy fields for compatibility
  applicationName?: string;
  ownerEmail?: string;
  ownerName?: string;
  allowedRedirectUris?: string[];
  allowedScopes?: string[];
  lastActivity?: string;
  stats?: {
    totalUsers: number;
    monthlyRequests: number;
    lastRequest: string;
  };
}

interface ApplicationFilters {
  status: 'all' | 'pending' | 'approved' | 'rejected' | 'suspended';
  activity: 'all' | 'active' | 'inactive';
  dateRange: 'all' | 'today' | 'week' | 'month' | 'year';
  searchTerm: string;
}

/**
 * Admin Application Management
 * 
 * System-level application management interface for administrators to oversee
 * all registered applications, approve registrations, and manage configurations.
 */
const AdminApplicationManagement: React.FC = () => {
  const intl = useIntl();
  const [applications, setApplications] = useState<AdminApplication[]>([]);
  const [filteredApplications, setFilteredApplications] = useState<AdminApplication[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedApplication, setSelectedApplication] = useState<AdminApplication | null>(null);
  const [actionMenuOpen, setActionMenuOpen] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  
  const [filters, setFilters] = useState<ApplicationFilters>({
    status: 'all',
    activity: 'all',
    dateRange: 'all',
    searchTerm: ''
  });

  // Enhanced filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [approvalFilter, setApprovalFilter] = useState<string>('all');

  // Fetch applications from API
  useEffect(() => {
    const fetchApplications = async () => {
      setIsLoading(true);
      try {
        const response = await fetch('/api/v1/admin/applications');
        if (!response.ok) {
          throw new Error('Failed to fetch applications');
        }
        const data = await response.json();

        // Transform API response to match frontend interface
        const transformedApplications: AdminApplication[] = data.map((item: any) => ({
          id: item.id,
          name: item.application_name,
          clientId: item.client_id,
          description: item.description || `${intl.formatMessage({ id: 'admin.applications.card.oauthClient' })} ${item.application_name}`,
          redirectUris: item.allowed_redirect_uris || [],
          scopes: item.allowed_scopes || [],
          status: item.status,
          isActive: item.is_active,
          isApproved: item.is_approved,
          createdAt: item.created_at,
          updatedAt: item.updated_at || item.created_at,
          developer: {
            id: 'dev-' + item.id,
            username: item.owner_name || intl.formatMessage({ id: 'admin.applications.defaultOwner' }),
            email: item.owner_email || '<EMAIL>'
          },
          usage: {
            totalUsers: item.stats?.total_users || 0,
            activeUsers: Math.floor((item.stats?.total_users || 0) * 0.7),
            requestsToday: Math.floor((item.stats?.monthly_requests || 0) / 30),
            requestsThisMonth: item.stats?.monthly_requests || 0
          },
          // Legacy fields for compatibility
          applicationName: item.application_name,
          ownerEmail: item.owner_email || '<EMAIL>',
          ownerName: item.owner_name || intl.formatMessage({ id: 'admin.applications.defaultOwner' }),
          allowedRedirectUris: item.allowed_redirect_uris || [],
          allowedScopes: item.allowed_scopes || [],
          lastActivity: item.stats?.last_request,
          stats: item.stats ? {
            totalUsers: item.stats.total_users,
            monthlyRequests: item.stats.monthly_requests,
            lastRequest: item.stats.last_request
          } : undefined
        }));

        setApplications(transformedApplications);
        setFilteredApplications(transformedApplications);
      } catch (error) {
        console.error('Error fetching applications:', error);
        // Fallback to empty array on error
        setApplications([]);
        setFilteredApplications([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchApplications();
  }, []);

  // Apply filters
  useEffect(() => {
    let filtered = [...applications];

    // Search filter
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(application =>
        (application.name || application.applicationName || '').toLowerCase().includes(searchLower) ||
        application.description.toLowerCase().includes(searchLower) ||
        (application.developer?.email || application.ownerEmail || '').toLowerCase().includes(searchLower) ||
        (application.developer?.username || application.ownerName || '').toLowerCase().includes(searchLower) ||
        application.clientId.toLowerCase().includes(searchLower)
      );
    }

    // Status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(application => application.status === filters.status);
    }

    // Activity filter
    if (filters.activity !== 'all') {
      switch (filters.activity) {
        case 'active':
          filtered = filtered.filter(application => application.isActive && (application.usage?.requestsThisMonth || application.stats?.monthlyRequests || 0) > 0);
          break;
        case 'inactive':
          filtered = filtered.filter(application => !application.isActive || (application.usage?.requestsThisMonth || application.stats?.monthlyRequests || 0) === 0);
          break;
      }
    }

    // Date range filter
    if (filters.dateRange !== 'all') {
      const now = new Date();
      const filterDate = new Date();
      
      switch (filters.dateRange) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          break;
        case 'year':
          filterDate.setFullYear(now.getFullYear() - 1);
          break;
      }
      
      filtered = filtered.filter(application => new Date(application.createdAt) >= filterDate);
    }

    setFilteredApplications(filtered);
  }, [applications, filters]);

  const handleApplicationAction = async (action: string, applicationId: string) => {
    const application = applications.find(s => s.id === applicationId);
    if (!application) return;

    try {
      switch (action) {
        case 'approve':
          await approveAdminApplication(applicationId);
          setApplications(prev => prev.map(s =>
            s.id === applicationId ? { ...s, status: 'approved', isApproved: true, isActive: true } : s
          ));
          console.log(`Application ${application.name || application.applicationName} has been approved`);
          break;

        case 'reject':
          await rejectAdminApplication(applicationId, 'Rejected by admin');
          setApplications(prev => prev.map(s =>
            s.id === applicationId ? { ...s, status: 'rejected', isApproved: false, isActive: false } : s
          ));
          console.log(`Application ${application.name || application.applicationName} has been rejected`);
          break;

        case 'suspend':
          await suspendAdminApplication(applicationId, 'Suspended by admin');
          setApplications(prev => prev.map(s =>
            s.id === applicationId ? { ...s, isActive: false } : s
          ));
          console.log(`Application ${application.name || application.applicationName} has been suspended`);
          break;

        case 'reactivate':
          await reactivateAdminApplication(applicationId);
          setApplications(prev => prev.map(s =>
            s.id === applicationId ? { ...s, isActive: true } : s
          ));
          console.log(`Application ${application.name || application.applicationName} has been reactivated`);
          break;

        case 'view_details':
          setSelectedApplication(application);
          break;

        case 'delete':
          if (window.confirm('Are you sure you want to permanently delete this application? This action cannot be undone.')) {
            // TODO: Add delete API call when available
            setApplications(prev => prev.filter(s => s.id !== applicationId));
            console.log(`Application ${application.name || application.applicationName} has been deleted`);
          }
          break;
      }
    } catch (error) {
      console.error(`Failed to perform action ${action} for application ${applicationId}:`, error);
      alert(`Failed to ${action} application. Please try again.`);
    }

    setActionMenuOpen(null);
  };

  // Filter categories
  const appFilterCategories = getApplicationFilterCategories(
    intl,
    statusFilter,
    approvalFilter,
    (value: string | string[]) => setStatusFilter(Array.isArray(value) ? value[0] : value),
    (value: string | string[]) => setApprovalFilter(Array.isArray(value) ? value[0] : value)
  );

  // Reset functions
  const resetAppFilters = () => {
    setSearchTerm('');
    setStatusFilter('all');
    setApprovalFilter('all');
  };

  // Enhanced filtered applications
  const enhancedFilteredApplications = useMemo(() => {
    try {
      return filterApplications(applications, searchTerm, statusFilter, approvalFilter);
    } catch (error) {
      console.error('Error filtering applications:', error);
      return [];
    }
  }, [applications, searchTerm, statusFilter, approvalFilter]);

  const exportAllApplications = () => {
    // TODO: Implement bulk export
    console.log('Exporting all applications...');
  };

  const getApplicationStatus = (application: AdminApplication): string => {
    // Derive status from available fields
    if (!application.isApproved) {
      return 'pending';
    }
    if (!application.isActive) {
      return 'suspended';
    }
    return 'approved';
  };

  const getStatusBadge = (application: AdminApplication) => {
    const status = getApplicationStatus(application);
    const statusConfig: Record<string, { color: string; icon: any }> = {
      pending: { color: 'bg-amber-100 text-amber-700', icon: Clock },
      approved: { color: 'bg-green-100 text-green-700', icon: CheckCircle },
      rejected: { color: 'bg-red-100 text-red-700', icon: XCircle },
      suspended: { color: 'bg-orange-100 text-orange-700', icon: AlertTriangle }
    };

    const config = statusConfig[status] || { color: 'bg-gray-100 text-gray-700', icon: AlertTriangle };
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center gap-1 rounded-full px-2 py-1 text-xs font-medium ${config.color}`}>
        <Icon className="h-3 w-3" />
        {intl.formatMessage({ id: `admin.applications.status.${status}` })}
      </span>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatLastActivity = (activityText: string) => {
    if (activityText === 'Never') {
      return intl.formatMessage({ id: 'admin.applications.time.never' });
    }

    // Handle "X days ago" pattern
    const daysMatch = activityText.match(/^(\d+) days ago$/);
    if (daysMatch) {
      const count = parseInt(daysMatch[1]);
      return intl.formatMessage({ id: 'admin.applications.time.daysAgo' }, { count });
    }

    // Handle "1 day ago"
    if (activityText === '1 day ago') {
      return intl.formatMessage({ id: 'admin.applications.time.dayAgo' });
    }

    // Handle "X hours ago" pattern
    const hoursMatch = activityText.match(/^(\d+) hours ago$/);
    if (hoursMatch) {
      const count = parseInt(hoursMatch[1]);
      return intl.formatMessage({ id: 'admin.applications.time.hoursAgo' }, { count });
    }

    // Handle "1 hour ago"
    if (activityText === '1 hour ago') {
      return intl.formatMessage({ id: 'admin.applications.time.hourAgo' });
    }

    // Handle "X minutes ago" pattern
    const minutesMatch = activityText.match(/^(\d+) minutes ago$/);
    if (minutesMatch) {
      const count = parseInt(minutesMatch[1]);
      return intl.formatMessage({ id: 'admin.applications.time.minutesAgo' }, { count });
    }

    // Handle "1 minute ago"
    if (activityText === '1 minute ago') {
      return intl.formatMessage({ id: 'admin.applications.time.minuteAgo' });
    }

    // Handle "Just now"
    if (activityText === 'Just now') {
      return intl.formatMessage({ id: 'admin.applications.time.justNow' });
    }

    // Fallback to original text if no pattern matches
    return activityText;
  };

  if (isLoading) {
    return <PageLoader />;
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{intl.formatMessage({ id: 'admin.applications.title' })}</h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'admin.applications.subtitle' })}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={exportAllApplications}
            className="inline-flex items-center gap-2 rounded-md border border-input px-4 py-2 text-sm font-medium hover:bg-accent transition-colors"
          >
            <Download className="h-4 w-4" />
            {intl.formatMessage({ id: 'admin.applications.exportAll' })}
          </button>
          <button
            onClick={() => window.location.reload()}
            className="inline-flex items-center gap-2 rounded-md border border-input px-4 py-2 text-sm font-medium hover:bg-accent transition-colors"
          >
            <RefreshCw className="h-4 w-4" />
            {intl.formatMessage({ id: 'admin.applications.refresh' })}
          </button>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <div className="rounded-lg border bg-card p-4 text-card-foreground shadow-sm">
          <div className="flex items-center gap-2 mb-2">
            <Settings className="h-4 w-4 text-blue-600" />
            <span className="text-sm font-medium">{intl.formatMessage({ id: 'admin.applications.stats.totalApplications' })}</span>
          </div>
          <p className="text-2xl font-bold">{applications.length}</p>
        </div>

        <div className="rounded-lg border bg-card p-4 text-card-foreground shadow-sm">
          <div className="flex items-center gap-2 mb-2">
            <Clock className="h-4 w-4 text-amber-600" />
            <span className="text-sm font-medium">{intl.formatMessage({ id: 'admin.applications.stats.pendingApproval' })}</span>
          </div>
          <p className="text-2xl font-bold">{applications.filter(s => s.status === 'pending').length}</p>
        </div>

        <div className="rounded-lg border bg-card p-4 text-card-foreground shadow-sm">
          <div className="flex items-center gap-2 mb-2">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <span className="text-sm font-medium">{intl.formatMessage({ id: 'admin.applications.stats.active' })}</span>
          </div>
          <p className="text-2xl font-bold">{applications.filter(s => s.isActive).length}</p>
        </div>

        <div className="rounded-lg border bg-card p-4 text-card-foreground shadow-sm">
          <div className="flex items-center gap-2 mb-2">
            <Users className="h-4 w-4 text-purple-600" />
            <span className="text-sm font-medium">{intl.formatMessage({ id: 'admin.applications.stats.totalUsers' })}</span>
          </div>
          <p className="text-2xl font-bold">
            {applications.reduce((sum, s) => sum + (s.usage?.totalUsers || s.stats?.totalUsers || 0), 0).toLocaleString()}
          </p>
        </div>
      </div>

      {/* Enhanced Filter Panel */}
      <FilterPanel
        title={intl.formatMessage({ id: 'admin.applications.filter.title' })}
        filterCategories={appFilterCategories}
        searchQuery={searchTerm}
        onSearchChange={setSearchTerm}
        onClearSearch={() => setSearchTerm('')}
        onResetAll={resetAppFilters}
        variant="default"
        showIcons={true}
        showSearchBar={true}
        searchPlaceholder={intl.formatMessage({ id: 'admin.applications.filter.searchPlaceholder' })}
        searchHotkey="cmd+f"
        size="md"
      />

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          {intl.formatMessage({ id: 'admin.applications.results.showing' }, { count: enhancedFilteredApplications.length, total: applications.length })}
        </p>
      </div>

      {/* Applications List */}
      <div className="space-y-4">
        {enhancedFilteredApplications.length === 0 ? (
          <div className="rounded-lg border bg-card p-12 text-center">
            <Settings className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">{intl.formatMessage({ id: 'admin.applications.empty.title' })}</h3>
            <p className="text-muted-foreground">
              {searchTerm || statusFilter !== 'all' || approvalFilter !== 'all'
                ? intl.formatMessage({ id: 'admin.applications.empty.filtered' })
                : intl.formatMessage({ id: 'admin.applications.empty.noApplications' })}
            </p>
          </div>
        ) : (
          enhancedFilteredApplications.map((application) => (
            <div key={application.id} className="rounded-lg border bg-card p-6 text-card-foreground shadow-sm">
              <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
                <div className="flex items-start gap-4 flex-1 min-w-0">
                  <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center flex-shrink-0">
                    <Settings className="h-6 w-6 text-primary" />
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-2">
                      <h3 className="text-lg font-semibold truncate">{application.applicationName}</h3>
                      <div className="flex gap-2 flex-wrap">
                        {getStatusBadge(application)}
                        {application.isActive && (
                          <span className="inline-flex items-center gap-1 rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-700">
                            <Activity className="h-3 w-3" />
                            {intl.formatMessage({ id: 'admin.applications.card.active' })}
                          </span>
                        )}
                      </div>
                    </div>

                    <p className="text-sm text-muted-foreground mb-3">{application.description}</p>

                    <div className="grid gap-2 md:grid-cols-2 text-sm text-muted-foreground mb-4">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        {intl.formatMessage({ id: 'admin.applications.card.owner' })}: {application.ownerName} ({application.ownerEmail})
                      </div>
                      <div className="flex items-center gap-2">
                        <Key className="h-4 w-4" />
                        {intl.formatMessage({ id: 'admin.applications.card.clientId' })}: {application.clientId}
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        {intl.formatMessage({ id: 'admin.applications.card.created' })}: {formatDate(application.createdAt)}
                      </div>
                      <div className="flex items-center gap-2">
                        <Activity className="h-4 w-4" />
                        {intl.formatMessage({ id: 'admin.applications.card.lastActivity' })}: {formatLastActivity(application.lastActivity)}
                      </div>
                    </div>

                    {/* Statistics */}
                    <div className="grid gap-4 md:grid-cols-3 mb-4">
                      <div className="rounded-lg bg-muted/50 p-3">
                        <div className="flex items-center gap-2 mb-1">
                          <Users className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm font-medium">{intl.formatMessage({ id: 'admin.applications.card.users' })}</span>
                        </div>
                        <p className="text-lg font-bold">{(application.usage?.totalUsers || application.stats?.totalUsers || 0).toLocaleString()}</p>
                      </div>

                      <div className="rounded-lg bg-muted/50 p-3">
                        <div className="flex items-center gap-2 mb-1">
                          <BarChart3 className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm font-medium">{intl.formatMessage({ id: 'admin.applications.card.requests' })}</span>
                        </div>
                        <p className="text-lg font-bold">{(application.usage?.requestsThisMonth || application.stats?.monthlyRequests || 0).toLocaleString()}</p>
                      </div>

                      <div className="rounded-lg bg-muted/50 p-3">
                        <div className="flex items-center gap-2 mb-1">
                          <Globe className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm font-medium">{intl.formatMessage({ id: 'admin.applications.card.redirectUris' })}</span>
                        </div>
                        <p className="text-lg font-bold">{application.allowedRedirectUris.length}</p>
                      </div>
                    </div>

                    {/* Scopes */}
                    <div className="mb-4">
                      <p className="text-sm font-medium mb-2">{intl.formatMessage({ id: 'admin.applications.card.allowedScopes' })}:</p>
                      <div className="flex flex-wrap gap-1">
                        {application.allowedScopes.map((scope: string) => (
                          <span
                            key={scope}
                            className="inline-flex items-center rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-700"
                          >
                            {scope}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="relative flex-shrink-0 self-start">
                  <button
                    onClick={() => setActionMenuOpen(actionMenuOpen === application.id ? null : application.id)}
                    className="rounded-md border border-input p-2 hover:bg-accent transition-colors"
                  >
                    <MoreVertical className="h-4 w-4" />
                  </button>

                  {actionMenuOpen === application.id && (
                    <div className="absolute right-0 top-full mt-1 w-48 rounded-md border bg-popover p-1 shadow-sm z-10">
                      <button
                        onClick={() => handleApplicationAction('view_details', application.id)}
                        className="flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-sm hover:bg-accent"
                      >
                        <Eye className="h-4 w-4" />
                        {intl.formatMessage({ id: 'admin.applications.actions.viewDetails' })}
                      </button>

                      {application.status === 'pending' && (
                        <>
                          <button
                            onClick={() => handleApplicationAction('approve', application.id)}
                            className="flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-sm text-green-600 hover:bg-green-50"
                          >
                            <CheckCircle className="h-4 w-4" />
                            {intl.formatMessage({ id: 'admin.applications.actions.approve' })}
                          </button>
                          <button
                            onClick={() => handleApplicationAction('reject', application.id)}
                            className="flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-sm text-red-600 hover:bg-red-50"
                          >
                            <XCircle className="h-4 w-4" />
                            {intl.formatMessage({ id: 'admin.applications.actions.reject' })}
                          </button>
                        </>
                      )}

                      {application.isActive && (
                        <>
                          <button
                            onClick={() => handleApplicationAction('suspend', application.id)}
                            className="flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-sm text-orange-600 hover:bg-orange-50"
                          >
                            <PowerOff className="h-4 w-4" />
                            {intl.formatMessage({ id: 'admin.applications.actions.suspend' })}
                          </button>
                        </>
                      )}

                      {!application.isActive && (
                        <button
                          onClick={() => handleApplicationAction('reactivate', application.id)}
                          className="flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-sm text-green-600 hover:bg-green-50"
                        >
                          <Power className="h-4 w-4" />
                          {intl.formatMessage({ id: 'admin.applications.actions.reactivate' })}
                        </button>
                      )}

                      <div className="my-1 h-px bg-border" />
                      <button
                        onClick={() => handleApplicationAction('delete', application.id)}
                        className="flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-sm text-red-600 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                        {intl.formatMessage({ id: 'admin.applications.actions.delete' })}
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Application Details Dialog */}
      <ApplicationDetailsDialog
        isOpen={selectedApplication !== null}
        onClose={() => setSelectedApplication(null)}
        application={selectedApplication}
        onApplicationAction={handleApplicationAction}
      />
    </div>
  );
};

export default AdminApplicationManagement;
