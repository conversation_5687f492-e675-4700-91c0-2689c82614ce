import React, { useState, useEffect, useMemo } from 'react';
import { useIntl } from 'react-intl';
import { useAdminUsers } from '@/hooks/use-admin-api';
import { suspendAdminUser, reactivateAdminUser } from '@/services/admin-api';
import {
  Search,
  Filter,
  MoreVertical,
  Eye,
  UserCheck,
  UserX,
  Key,
  Download,
  RefreshCw,
  Calendar,
  Mail,
  Phone,
  MapPin,
  Activity,
  Shield,
  AlertTriangle,
  CheckCircle,
  Clock,
  Users,
  Settings
} from 'lucide-react';

import { PageLoader } from '@/components/base/page-loader';
import FilterPanel from '@/components/common/filter-panel';
import { UserDetailsDialog } from '@/components/features/admin/user-details-dialog';
import {
  getUserFilterCategories,
  filterUsers
} from '@/data/admin-filters';

interface User {
  id: string;
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  isActive: boolean;
  isVerified: boolean;
  createdAt: string;
  lastLogin: string | null;
  connectedApps: string[];
  loginAttempts: number;
  role: 'user' | 'developer' | 'admin';
  location?: string;
  phone?: string;
}

interface UserFilters {
  status: 'all' | 'active' | 'inactive' | 'unverified';
  role: 'all' | 'user' | 'developer' | 'admin';
  dateRange: 'all' | 'today' | 'week' | 'month' | 'year';
  searchTerm: string;
}

/**
 * Admin User Management
 * 
 * Comprehensive user administration interface for system administrators
 * to manage user accounts, view activity, and perform administrative actions.
 */
const AdminUserManagement: React.FC = () => {
  const intl = useIntl();
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [actionMenuOpen, setActionMenuOpen] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  
  const [filters, setFilters] = useState<UserFilters>({
    status: 'all',
    role: 'all',
    dateRange: 'all',
    searchTerm: ''
  });

  // Enhanced filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [roleFilters, setRoleFilters] = useState<string[]>([]);

  // Use the admin API hook to fetch all users (developers + regular users)
  const { users: adminUsers, isLoading: adminLoading, error: adminError, loadUsers } = useAdminUsers();

  // Load users on component mount
  useEffect(() => {
    loadUsers();
  }, [loadUsers]);

  // Transform admin users to match the expected User interface
  useEffect(() => {
    if (adminUsers && Array.isArray(adminUsers)) {
      const transformedUsers = adminUsers.map(user => ({
        id: user.id,
        email: user.email,
        username: user.username || user.email.split('@')[0], // use username from API or fallback to email prefix
        firstName: user.first_name || '', // use first_name from API
        lastName: user.last_name || '', // use last_name from API
        isActive: user.is_active || false,
        isVerified: true, // assume verified since they're in the system
        role: (user.role || 'user') as 'user' | 'developer' | 'admin', // use actual role from API, default to 'user'
        createdAt: user.created_at || new Date().toISOString(),
        lastLogin: user.last_login || null,
        connectedApps: [], // default to empty array
        loginAttempts: user.application_count || 0, // use application_count as a proxy
      }));
      setUsers(transformedUsers);
      setFilteredUsers(transformedUsers);
    }
  }, [adminUsers]);

  // Update loading state
  useEffect(() => {
    setIsLoading(adminLoading);
  }, [adminLoading]);

  // Filter categories
  const userFilterCategories = getUserFilterCategories(
    intl,
    statusFilter,
    roleFilters,
    (selected: string | string[]) => typeof selected === 'string' ? setStatusFilter(selected) : setStatusFilter(selected[0] || 'all'),
    (selected: string | string[]) => Array.isArray(selected) ? setRoleFilters(selected) : setRoleFilters([selected])
  );

  // Reset functions
  const resetUserFilters = () => {
    setSearchTerm('');
    setStatusFilter('all');
    setRoleFilters([]);
  };

  // Enhanced filtered users
  const enhancedFilteredUsers = useMemo(() => {
    try {
      return filterUsers(users, searchTerm, statusFilter, roleFilters);
    } catch (error) {
      console.error('Error filtering users:', error);
      return [];
    }
  }, [users, searchTerm, statusFilter, roleFilters]);

  // Apply filters (legacy)
  useEffect(() => {
    let filtered = [...users];

    // Search filter
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(user => 
        user.email.toLowerCase().includes(searchLower) ||
        user.username.toLowerCase().includes(searchLower) ||
        user.firstName.toLowerCase().includes(searchLower) ||
        user.lastName.toLowerCase().includes(searchLower)
      );
    }

    // Status filter
    if (filters.status !== 'all') {
      switch (filters.status) {
        case 'active':
          filtered = filtered.filter(user => user.isActive && user.isVerified);
          break;
        case 'inactive':
          filtered = filtered.filter(user => !user.isActive);
          break;
        case 'unverified':
          filtered = filtered.filter(user => !user.isVerified);
          break;
      }
    }

    // Role filter
    if (filters.role !== 'all') {
      filtered = filtered.filter(user => user.role === filters.role);
    }

    // Date range filter
    if (filters.dateRange !== 'all') {
      const now = new Date();
      const filterDate = new Date();
      
      switch (filters.dateRange) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          break;
        case 'year':
          filterDate.setFullYear(now.getFullYear() - 1);
          break;
      }
      
      filtered = filtered.filter(user => new Date(user.createdAt) >= filterDate);
    }

    setFilteredUsers(filtered);
  }, [users, filters]);

  const handleUserAction = async (action: string, userId: string) => {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    switch (action) {
      case 'toggle_status':
        try {
          if (user.isActive) {
            // Suspend user
            await suspendAdminUser(userId, 'Suspended by admin');
            setUsers(prev => prev.map(u =>
              u.id === userId ? { ...u, isActive: false } : u
            ));
            console.log(`User ${user.username} has been suspended`);
          } else {
            // Reactivate user
            await reactivateAdminUser(userId);
            setUsers(prev => prev.map(u =>
              u.id === userId ? { ...u, isActive: true } : u
            ));
            console.log(`User ${user.username} has been reactivated`);
          }
        } catch (error) {
          console.error(`Failed to toggle user status:`, error);
          alert(`Failed to ${user.isActive ? 'suspend' : 'reactivate'} user. Please try again.`);
        }
        break;
      case 'reset_password':
        if (window.confirm(`Are you sure you want to reset the password for ${user.username}? They will receive an email with reset instructions.`)) {
          try {
            // TODO: Replace with actual API call when backend endpoints are implemented
            // const response = await fetch(`/api/v1/admin/users/${userId}/reset-password`, {
            //   method: 'POST',
            //   headers: { 'Content-Type': 'application/json' }
            // });
            // if (!response.ok) throw new Error('Failed to reset password');

            console.log('Password reset initiated for user:', userId);
            alert(`Password reset email has been sent to ${user.email}`);
          } catch (error) {
            console.error('Error resetting password:', error);
            alert('Failed to reset password. Please try again.');
          }
        }
        break;
      case 'view_details':
        setSelectedUser(user);
        break;
      case 'export_data':
        try {
          // Try to export user data via API
          const response = await fetch(`/api/v1/admin/users/${userId}/export`, {
            method: 'GET',
            headers: { 'Accept': 'application/json' }
          });

          let userData;
          if (response.ok) {
            userData = await response.json();
          } else {
            // API not available - create basic export from available user data
            userData = {
              id: user.id,
              username: user.username,
              email: user.email,
              firstName: user.firstName,
              lastName: user.lastName,
              role: user.role,
              isActive: user.isActive,
              isVerified: user.isVerified,
              createdAt: user.createdAt,
              lastLogin: user.lastLogin,
              exportedAt: new Date().toISOString(),
              note: 'Exported from frontend data - full export requires API implementation'
            };
          }

          // Create and download JSON file
          const dataStr = JSON.stringify(userData, null, 2);
          const dataBlob = new Blob([dataStr], { type: 'application/json' });
          const url = URL.createObjectURL(dataBlob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `user_${user.username}_export_${new Date().toISOString().split('T')[0]}.json`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url);

          console.log('User data exported for:', userId);
        } catch (error) {
          console.error('Error exporting user data:', error);
          alert('Failed to export user data. Please try again.');
        }
        break;
    }
    setActionMenuOpen(null);
  };

  const exportAllUsers = () => {
    try {
      // TODO: Replace with actual API call when backend endpoints are implemented
      // const response = await fetch('/api/v1/admin/users/export', {
      //   method: 'GET',
      //   headers: { 'Accept': 'application/json' }
      // });
      // if (!response.ok) throw new Error('Failed to export all users');
      // const allUsersData = await response.json();

      // For now, export current filtered users
      const exportData = {
        exportedAt: new Date().toISOString(),
        totalUsers: filteredUsers.length,
        filters: {
          search: filters.searchTerm,
          role: filters.role,
          status: filters.status
        },
        users: filteredUsers.map(user => ({
          id: user.id,
          username: user.username,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          isActive: user.isActive,
          isVerified: user.isVerified,
          createdAt: user.createdAt,
          lastLogin: user.lastLogin
        }))
      };

      // Create and download JSON file
      const dataStr = JSON.stringify(exportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `all_users_export_${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      console.log('All users exported successfully');
      alert(`Exported ${filteredUsers.length} users successfully!`);
    } catch (error) {
      console.error('Error exporting all users:', error);
      alert('Failed to export users. Please try again.');
    }
  };

  const getUserStatusBadge = (user: User) => {
    if (!user.isVerified) {
      return (
        <span className="inline-flex items-center gap-1 rounded-full bg-amber-100 px-2 py-1 text-xs font-medium text-amber-700">
          <Clock className="h-3 w-3" />
          {intl.formatMessage({ id: 'admin.users.status.unverified' })}
        </span>
      );
    }

    if (!user.isActive) {
      return (
        <span className="inline-flex items-center gap-1 rounded-full bg-red-100 px-2 py-1 text-xs font-medium text-red-700">
          <UserX className="h-3 w-3" />
          {intl.formatMessage({ id: 'admin.users.status.inactive' })}
        </span>
      );
    }

    if (user.loginAttempts > 0) {
      return (
        <span className="inline-flex items-center gap-1 rounded-full bg-orange-100 px-2 py-1 text-xs font-medium text-orange-700">
          <AlertTriangle className="h-3 w-3" />
          {intl.formatMessage({ id: 'admin.users.status.warning' })}
        </span>
      );
    }

    return (
      <span className="inline-flex items-center gap-1 rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-700">
        <CheckCircle className="h-3 w-3" />
        {intl.formatMessage({ id: 'admin.users.status.active' })}
      </span>
    );
  };

  const getRoleBadge = (role: User['role']) => {
    const roleConfig = {
      admin: { color: 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-200', icon: Shield },
      developer: { color: 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-200', icon: Settings },
      user: { color: 'bg-muted text-muted-foreground', icon: Users }
    };

    const config = roleConfig[role];
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center gap-1 rounded-full px-2 py-1 text-xs font-medium ${config.color}`}>
        <Icon className="h-3 w-3" />
        {intl.formatMessage({ id: `admin.users.role.${role}` })}
      </span>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return <PageLoader />;
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{intl.formatMessage({ id: 'admin.users.title' })}</h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'admin.users.subtitle' })}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={exportAllUsers}
            className="inline-flex items-center gap-2 rounded-md border border-input px-4 py-2 text-sm font-medium hover:bg-accent transition-colors"
          >
            <Download className="h-4 w-4" />
            {intl.formatMessage({ id: 'admin.users.exportAll' })}
          </button>
          <button
            onClick={() => window.location.reload()}
            className="inline-flex items-center gap-2 rounded-md border border-input px-4 py-2 text-sm font-medium hover:bg-accent transition-colors"
          >
            <RefreshCw className="h-4 w-4" />
            {intl.formatMessage({ id: 'admin.users.refresh' })}
          </button>
        </div>
      </div>

      {/* Enhanced Filter Panel */}
      <FilterPanel
        title={intl.formatMessage({ id: 'admin.users.filter.title' })}
        filterCategories={userFilterCategories}
        searchQuery={searchTerm}
        onSearchChange={setSearchTerm}
        onClearSearch={() => setSearchTerm('')}
        onResetAll={resetUserFilters}
        variant="default"
        showIcons={true}
        showSearchBar={true}
        searchPlaceholder={intl.formatMessage({ id: 'admin.users.filter.searchPlaceholder' })}
        searchHotkey="cmd+f"
        size="md"
      />
      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          {intl.formatMessage({ id: 'admin.users.results.showing' }, { count: enhancedFilteredUsers.length, total: users.length })}
        </p>
      </div>

      {/* Users List */}
      <div className="space-y-4">
        {enhancedFilteredUsers.length === 0 ? (
          <div className="rounded-lg border bg-card p-12 text-center">
            <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">{intl.formatMessage({ id: 'admin.users.empty.title' })}</h3>
            <p className="text-muted-foreground">
              {searchTerm || statusFilter !== 'all' || roleFilters.length > 0
                ? intl.formatMessage({ id: 'admin.users.empty.filtered' })
                : intl.formatMessage({ id: 'admin.users.empty.noUsers' })}
            </p>
          </div>
        ) : (
          enhancedFilteredUsers.map((user) => (
            <div key={user.id} className="rounded-lg border bg-card p-6 text-card-foreground shadow-sm">
              <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
                <div className="flex items-start gap-4 flex-1 min-w-0">
                  <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                    <span className="text-lg font-semibold text-primary">
                      {user.firstName.charAt(0)}{user.lastName.charAt(0)}
                    </span>
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-2">
                      <h3 className="text-lg font-semibold truncate">
                        {user.firstName} {user.lastName}
                      </h3>
                      <div className="flex gap-2 flex-wrap">
                        {getUserStatusBadge(user)}
                        {getRoleBadge(user.role)}
                      </div>
                    </div>

                    <div className="grid gap-2 sm:grid-cols-1 lg:grid-cols-2 text-sm text-muted-foreground">
                      <div className="flex items-center gap-2 min-w-0">
                        <Mail className="h-4 w-4 flex-shrink-0" />
                        <span className="truncate">{user.email}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 flex-shrink-0" />
                        <span>{intl.formatMessage({ id: 'admin.users.card.joined' })} {formatDate(user.createdAt)}</span>
                      </div>
                      {user.phone && (
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 flex-shrink-0" />
                          <span>{user.phone}</span>
                        </div>
                      )}
                      {user.location && (
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4 flex-shrink-0" />
                          <span>{user.location}</span>
                        </div>
                      )}
                      <div className="flex items-center gap-2">
                        <Activity className="h-4 w-4 flex-shrink-0" />
                        <span>{intl.formatMessage({ id: 'admin.users.card.lastLogin' })}: {user.lastLogin ? formatDate(user.lastLogin) : intl.formatMessage({ id: 'admin.users.card.lastLogin.never' })}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Settings className="h-4 w-4 flex-shrink-0" />
                        <span>{user.connectedApps.length} {intl.formatMessage({ id: 'admin.users.card.connectedApps' })}</span>
                      </div>
                    </div>

                    {user.connectedApps.length > 0 && (
                      <div className="mt-3">
                        <p className="text-sm font-medium mb-2">{intl.formatMessage({ id: 'admin.users.card.connectedApps.label' })}:</p>
                        <div className="flex flex-wrap gap-1">
                          {user.connectedApps.map((app: any) => (
                            <span
                              key={app}
                              className="inline-flex items-center rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-700"
                            >
                              {app}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                <div className="relative flex-shrink-0 self-start">
                  <button
                    onClick={() => setActionMenuOpen(actionMenuOpen === user.id ? null : user.id)}
                    className="rounded-md border border-input p-2 hover:bg-accent transition-colors"
                  >
                    <MoreVertical className="h-4 w-4" />
                  </button>

                  {actionMenuOpen === user.id && (
                    <div className="absolute right-0 top-full mt-1 w-48 rounded-md border bg-popover p-1 shadow-sm z-10">
                      <button
                        onClick={() => handleUserAction('view_details', user.id)}
                        className="flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-sm hover:bg-accent"
                      >
                        <Eye className="h-4 w-4" />
                        {intl.formatMessage({ id: 'admin.users.actions.viewDetails' })}
                      </button>
                      <button
                        onClick={() => handleUserAction('toggle_status', user.id)}
                        className="flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-sm hover:bg-accent"
                      >
                        {user.isActive ? (
                          <>
                            <UserX className="h-4 w-4" />
                            {intl.formatMessage({ id: 'admin.users.actions.disableAccount' })}
                          </>
                        ) : (
                          <>
                            <UserCheck className="h-4 w-4" />
                            {intl.formatMessage({ id: 'admin.users.actions.enableAccount' })}
                          </>
                        )}
                      </button>
                      <button
                        onClick={() => handleUserAction('reset_password', user.id)}
                        className="flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-sm hover:bg-accent"
                      >
                        <Key className="h-4 w-4" />
                        {intl.formatMessage({ id: 'admin.users.actions.resetPassword' })}
                      </button>
                      <div className="my-1 h-px bg-border" />
                      <button
                        onClick={() => handleUserAction('export_data', user.id)}
                        className="flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-sm hover:bg-accent"
                      >
                        <Download className="h-4 w-4" />
                        {intl.formatMessage({ id: 'admin.users.actions.exportData' })}
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* User Details Dialog */}
      <UserDetailsDialog
        isOpen={selectedUser !== null}
        onClose={() => setSelectedUser(null)}
        user={selectedUser}
        onUserAction={handleUserAction}
      />
    </div>
  );
};

export default AdminUserManagement;
