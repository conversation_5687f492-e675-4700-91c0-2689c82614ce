{"// Admin Applications Page": "", "admin.applications.title": "Application Management", "admin.applications.subtitle": "Manage all registered applications and applications across the platform", "admin.applications.refresh": "Refresh", "admin.applications.exportAll": "Export All", "// Summary Stats": "", "admin.applications.stats.totalApplications": "Total Applications", "admin.applications.stats.totalUsers": "Total Users", "admin.applications.stats.pendingApproval": "Pending Approval", "admin.applications.stats.active": "Active", "// Filter Panel": "", "admin.applications.filter.title": "Filter Applications", "admin.applications.filter.searchPlaceholder": "Search applications by name or owner...", "// Results Summary": "", "admin.applications.results.showing": "Showing {count} of {total} applications", "// Empty States": "", "admin.applications.empty.title": "No Applications Found", "admin.applications.empty.filtered": "No applications match your current filters", "admin.applications.empty.noApplications": "No applications have been registered yet", "// Status Badges": "", "admin.applications.status.pending": "Pending", "admin.applications.status.approved": "Approved", "admin.applications.status.rejected": "Rejected", "admin.applications.status.suspended": "Suspended", "// Application Card": "", "admin.applications.card.active": "Active", "admin.applications.card.owner": "Owner", "admin.applications.card.clientId": "Client ID", "admin.applications.card.created": "Created", "admin.applications.card.lastActivity": "Last activity", "admin.applications.card.users": "Users", "admin.applications.card.requests": "Monthly Requests", "admin.applications.card.redirectUris": "Redirect URIs", "admin.applications.card.allowedScopes": "Allowed Scopes", "admin.applications.card.oauthClient": "OAuth2 client for", "// Application Details Modal": "", "// Time/Date": "", "admin.applications.time.never": "Never", "admin.applications.time.daysAgo": "{count} days ago", "admin.applications.time.dayAgo": "1 day ago", "admin.applications.time.hoursAgo": "{count} hours ago", "admin.applications.time.hourAgo": "1 hour ago", "admin.applications.time.minutesAgo": "{count} minutes ago", "admin.applications.time.minuteAgo": "1 minute ago", "admin.applications.time.justNow": "Just now", "// Action Menu": "", "admin.applications.actions.viewDetails": "View Details", "admin.applications.actions.approve": "Approve", "admin.applications.actions.reject": "Reject", "admin.applications.actions.suspend": "Suspend", "admin.applications.actions.reactivate": "Reactivate", "admin.applications.actions.delete": "Delete", "// Default Values": "", "admin.applications.defaultOwner": "Developer", "admin.applications.details.title": "Application Details", "admin.applications.details.description": "View detailed information about this application", "admin.applications.details.clientId": "Client ID", "admin.applications.details.developer": "Developer", "admin.applications.details.created": "Created", "admin.applications.details.updated": "Last Updated", "admin.applications.details.usage": "Usage Statistics", "admin.applications.details.totalUsers": "Total Users", "admin.applications.details.activeUsers": "Active Users", "admin.applications.details.requestsToday": "Requests Today", "admin.applications.details.requestsThisMonth": "Requests This Month", "admin.applications.details.technical": "Technical Details", "admin.applications.details.redirectUris": "Redirect URIs", "admin.applications.details.scopes": "<PERSON><PERSON><PERSON>", "admin.applications.status.inactive": "Inactive", "admin.applications.status.${status}": "admin applications status [dynamic]", "admin.applications.actions.deleteApplication": "admin applications actions deleteApplication", "admin.applications.actions.editApplication": "admin applications actions editApplication", "admin.applications.actions.suspendApplication": "admin applications actions suspendApplication", "admin.applications.actions.viewAnalytics": "admin applications actions viewAnalytics", "admin.applications.card.uptime": "admin applications card uptime", "admin.applications.modal.allowedRedirectUris": "admin applications modal allowedRedirectUris", "admin.applications.modal.applicationName": "admin applications modal applicationName", "admin.applications.modal.clientId": "admin applications modal clientId", "admin.applications.modal.created": "admin applications modal created", "admin.applications.modal.description": "admin applications modal description", "admin.applications.modal.lastActivity": "admin applications modal lastActivity", "admin.applications.modal.owner": "admin applications modal owner", "admin.applications.modal.status": "admin applications modal status", "admin.applications.modal.title": "admin applications modal title", "admin.applications.stats.suspended": "admin applications stats suspended"}