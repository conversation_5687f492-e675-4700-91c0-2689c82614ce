{"// Admin Role Applications Page": "", "admin.developer-applications.title": "Developer Applications Review", "admin.developer-applications.subtitle": "Review and manage developer access applications", "// Statistics": "", "admin.developer-applications.stats.total": "Total Applications", "admin.developer-applications.stats.pending": "Pending Review", "admin.developer-applications.stats.approved": "Approved", "admin.developer-applications.stats.rejected": "Rejected", "// Filters": "", "admin.developer-applications.filters.title": "Filter Applications", "admin.developer-applications.filters.search": "Search by username or email...", "admin.developer-applications.filters.all": "All Status", "admin.developer-applications.filters.pending": "Pending", "admin.developer-applications.filters.approved": "Approved", "admin.developer-applications.filters.rejected": "Rejected", "// Application List": "", "admin.developer-applications.list.title": "Developer Applications", "admin.developer-applications.list.empty": "No applications found", "// Status": "", "// Actions": "", "admin.developer-applications.action.review": "Review", "admin.developer-applications.action.approve": "Approve", "admin.developer-applications.action.reject": "Reject", "// Review Dialog": "", "admin.developer-applications.review.title": "Review Developer Application", "admin.developer-applications.review.description": "Review the applicant's request for developer access and make a decision.", "admin.developer-applications.review.user": "Applicant", "admin.developer-applications.review.transition": "Role Transition", "admin.developer-applications.review.reason": "Application Reason", "admin.developer-applications.review.technical-background": "Technical Background", "admin.developer-applications.review.intended-use": "Intended Use", "admin.developer-applications.review.submitted": "Submitted At", "admin.developer-applications.review.status": "Current Status", "admin.developer-applications.review.notes": "Review Notes", "admin.developer-applications.review.notes.placeholder": "Add notes about your decision...", "admin.developer-applications.review.previous": "Previous Review Notes", "// Messages": "", "admin.developer-applications.error.load": "Failed to load applications", "admin.developer-applications.error.review": "Failed to review application", "admin.developer-applications.status.${application.status}": "admin developer-applications status [dynamic]", "admin.developer-applications.status.${selectedApplication.status}": "admin developer-applications status [dynamic]", "admin.developer-applications.status.approved": "admin developer-applications status approved", "admin.developer-applications.status.pending": "admin developer-applications status pending", "admin.developer-applications.status.rejected": "admin developer-applications status rejected", "admin.developer-applications.success.approved": "admin developer-applications success approved", "admin.developer-applications.success.rejected": "admin developer-applications success rejected"}