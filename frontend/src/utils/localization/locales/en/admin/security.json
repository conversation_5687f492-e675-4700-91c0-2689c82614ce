{"// Security Dashboard": "", "admin.security.dashboard.title": "Security Dashboard", "admin.security.dashboard.subtitle": "Monitor security threats and system activity", "admin.security.dashboard.overview": "Security Overview", "admin.security.dashboard.threat.level": "Threat Level", "admin.security.dashboard.total.threats": "Total Threats", "admin.security.dashboard.critical.threats": "Critical Threats", "admin.security.dashboard.high.threats": "High Threats", "admin.security.dashboard.view.all": "View All", "admin.security.dashboard.refresh": "Refresh", "admin.security.dashboard.last.updated": "Last Updated", "// Threat Levels": "", "// Threat Types": "", "admin.security.threat.type.suspicious.login": "Suspicious Login Activity", "admin.security.threat.type.brute.force": "Brute Force Attack", "admin.security.threat.type.account.takeover": "Account Takeover Attempt", "// Suspicious Login Activity": "", "admin.security.suspicious.login.empty": "No suspicious login activity detected", "// Brute Force Attacks": "", "admin.security.brute.force.empty": "No brute force attacks detected", "// Account Takeover": "", "admin.security.takeover.empty": "No account takeover attempts detected", "// MFA Bypass": "", "// Admin Activity": "", "// IP Reputation": "", "// Reputation Levels": "", "// Time Windows": "", "admin.security.time.window.30m": "Last 30 minutes", "admin.security.time.window.6h": "Last 6 hours", "admin.security.time.window.24h": "Last 24 hours", "// Actions": "", "// Alerts": "", "admin.security.alert.critical.threat": "Critical security threat requires immediate attention", "admin.security.alert.system.healthy": "Security system is operating normally", "// Messages": "", "admin.security.message.data.error": "Failed to load security data", "// Errors": "", "admin.security.error.data.unavailable": "Security data is currently unavailable", "// Common": "", "admin.security.common.loading": "Loading security data...", "admin.security.threat.level.${dashboardData.overview.threat_level}": "admin security threat level [dynamic]"}