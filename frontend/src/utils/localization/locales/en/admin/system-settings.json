{"// Admin System Settings Page": "", "admin.systemSettings.title": "System Settings", "admin.systemSettings.subtitle": "Configure enterprise security policies and system parameters", "admin.systemSettings.resetToDefaults": "Reset to Defaults", "admin.systemSettings.saveChanges": "Save Changes", "admin.systemSettings.saving": "Saving...", "// Tabs": "", "admin.systemSettings.tabs.authentication": "Authentication", "admin.systemSettings.tabs.security": "Security", "admin.systemSettings.tabs.system": "System", "admin.systemSettings.tabs.compliance": "Compliance", "admin.systemSettings.tabs.notifications": "Notifications", "admin.systemSettings.tabs.api": "API", "// Alerts": "", "admin.systemSettings.alerts.maintenanceMode": "Maintenance Mode Active:", "admin.systemSettings.alerts.maintenanceModeDescription": "The system is currently in maintenance mode. Users will see a maintenance page and cannot access the service.", "// Authentication Settings": "", "admin.systemSettings.authentication.title": "Authentication Configuration", "admin.systemSettings.authentication.description": "Configure authentication policies and password requirements", "admin.systemSettings.authentication.sessionTimeout": "Session Timeout (minutes)", "admin.systemSettings.authentication.sessionTimeoutHelp": "Automatic logout after inactivity", "admin.systemSettings.authentication.maxLoginAttempts": "<PERSON> Login Attempts", "admin.systemSettings.authentication.maxLoginAttemptsHelp": "Account lockout threshold", "admin.systemSettings.authentication.passwordPolicy": "Password Policy", "admin.systemSettings.authentication.passwordMinLength": "Minimum Length", "admin.systemSettings.authentication.passwordRequireSpecialChars": "Require Special Characters", "admin.systemSettings.authentication.passwordRequireSpecialCharsHelp": "Include symbols like !@#$%", "admin.systemSettings.authentication.passwordRequireNumbers": "Require Numbers", "admin.systemSettings.authentication.passwordRequireNumbersHelp": "Include at least one digit", "admin.systemSettings.authentication.passwordRequireUppercase": "Require Uppercase Letters", "admin.systemSettings.authentication.passwordRequireUppercaseHelp": "Include at least one capital letter", "admin.systemSettings.authentication.passwordExpiryDays": "Password Expiry (days)", "admin.systemSettings.authentication.passwordExpiryHelp": "Force password change interval", "admin.systemSettings.authentication.twoFactorRequired": "Require Two-Factor Authentication", "admin.systemSettings.authentication.twoFactorHelp": "Mandatory 2FA for all users", "// Security Policies": "", "admin.systemSettings.security.title": "Security Policies", "admin.systemSettings.security.description": "Configure security policies and access controls", "admin.systemSettings.security.allowPasswordReset": "Allow Password Reset", "admin.systemSettings.security.allowPasswordResetHelp": "Users can reset passwords via email", "admin.systemSettings.security.requireEmailVerification": "Require Email Verification", "admin.systemSettings.security.requireEmailVerificationHelp": "New accounts must verify email", "admin.systemSettings.security.allowSelfRegistration": "Allow Self Registration", "admin.systemSettings.security.allowSelfRegistrationHelp": "Users can create accounts independently", "admin.systemSettings.security.adminApprovalRequired": "<PERSON><PERSON> Required", "admin.systemSettings.security.adminApprovalRequiredHelp": "New accounts need admin approval", "admin.systemSettings.security.auditLogRetentionDays": "Audit Log Retention (days)", "admin.systemSettings.security.auditLogRetentionDaysHelp": "How long to keep security logs", "// System Configuration": "", "admin.systemSettings.system.title": "System Configuration", "admin.systemSettings.system.description": "Basic system settings and operational parameters", "admin.systemSettings.system.systemName": "System Name", "admin.systemSettings.system.systemNameHelp": "Display name for your SSO service", "admin.systemSettings.system.systemDescription": "System Description", "admin.systemSettings.system.systemDescriptionHelp": "Brief description of your service", "admin.systemSettings.system.supportEmail": "Support Email", "admin.systemSettings.system.supportEmailHelp": "Contact email for user support", "admin.systemSettings.system.maintenanceMode": "Maintenance Mode", "admin.systemSettings.system.maintenanceModeHelp": "Temporarily disable user access", "admin.systemSettings.system.debugMode": "Debug Mode", "admin.systemSettings.system.debugModeHelp": "Enable detailed logging (development only)", "admin.systemSettings.system.systemStatus": "System Status", "admin.systemSettings.system.active": "Active", "// Compliance Settings": "", "admin.systemSettings.compliance.title": "Compliance & Privacy", "admin.systemSettings.compliance.description": "Configure GDPR compliance and data protection settings", "admin.systemSettings.compliance.gdprComplianceMode": "GDPR Compliance Mode", "admin.systemSettings.compliance.gdprComplianceModeHelp": "Enable EU data protection features", "admin.systemSettings.compliance.dataRetentionDays": "Data Retention Period (days)", "admin.systemSettings.compliance.dataRetentionDaysHelp": "Automatic deletion of inactive user data", "admin.systemSettings.compliance.cookieConsentBanner": "<PERSON><PERSON>", "admin.systemSettings.compliance.cookieConsentBannerHelp": "Show cookie consent to users", "admin.systemSettings.compliance.privacyPolicyUrl": "Privacy Policy URL", "admin.systemSettings.compliance.privacyPolicyUrlHelp": "Link to your privacy policy", "admin.systemSettings.compliance.termsOfServiceUrl": "Terms of Service URL", "admin.systemSettings.compliance.termsOfServiceUrlHelp": "Link to your terms of service", "// Notification Settings": "", "admin.systemSettings.notifications.title": "Notification Configuration", "admin.systemSettings.notifications.description": "Configure system notifications and alerts", "admin.systemSettings.notifications.securityAlertsEnabledHelp": "Alert admins of security events", "admin.systemSettings.notifications.emailNotificationsEnabled": "Email Notifications", "admin.systemSettings.notifications.emailNotificationsEnabledHelp": "Send email notifications to users", "admin.systemSettings.notifications.securityAlertsEnabled": "Security Alerts", "admin.systemSettings.notifications.systemMaintenanceNotifications": "Maintenance Notifications", "admin.systemSettings.notifications.systemMaintenanceNotificationsHelp": "Notify users of system maintenance", "// API Settings": "", "admin.systemSettings.api.title": "API Configuration", "admin.systemSettings.api.description": "Configure API rate limiting and versioning", "admin.systemSettings.api.rateLimitEnabled": "Rate Limiting", "admin.systemSettings.api.rateLimitEnabledHelp": "Enable API rate limiting", "admin.systemSettings.api.rateLimitRequests": "Requests per Window", "admin.systemSettings.api.rateLimitRequestsHelp": "Maximum requests per window", "admin.systemSettings.api.rateLimitWindow": "Window Size (seconds)", "admin.systemSettings.api.rateLimitWindowHelp": "Time window for rate limiting", "admin.systemSettings.api.apiVersioning": "API Version", "admin.systemSettings.api.apiVersioningHelp": "Default API version for new integrations", "admin.systemSettings.api.version1": "Version 1.0 (Current)", "admin.systemSettings.api.version2": "Version 2.0 (Beta)", "// Toast Messages": "", "admin.systemSettings.toast.loadFailed.title": "Failed to Load Settings", "admin.systemSettings.toast.loadFailed.description": "Unable to load system settings. Please try again.", "admin.systemSettings.toast.saveSuccess.title": "Settings Saved", "admin.systemSettings.toast.saveSuccess.description": "System settings have been updated successfully.", "admin.systemSettings.toast.saveFailed.title": "Save Failed", "admin.systemSettings.toast.saveFailed.description": "Failed to save system settings. Please try again."}