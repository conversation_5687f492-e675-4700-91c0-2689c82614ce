{"// Admin Users Page": "", "admin.users.title": "User Management", "admin.users.subtitle": "Manage user accounts, permissions, and activity", "admin.users.refresh": "Refresh", "admin.users.exportAll": "Export All", "// Filter Panel": "", "admin.users.filter.title": "Filter Users", "admin.users.filter.searchPlaceholder": "Search users by email or name...", "// Results Summary": "", "admin.users.results.showing": "Showing {count} of {total} users", "// Empty States": "", "admin.users.empty.title": "No Users Found", "admin.users.empty.filtered": "No users match your current filters", "admin.users.empty.noUsers": "No users have been registered yet", "// User Card": "", "admin.users.card.joined": "Joined", "admin.users.card.lastLogin": "Last login", "admin.users.card.lastLogin.never": "Never", "admin.users.card.connectedApps": "connected apps", "admin.users.card.connectedApps.label": "Connected Applications", "// Status Badges": "", "admin.users.status.unverified": "Unverified", "admin.users.status.inactive": "Inactive", "admin.users.status.warning": "Warning", "admin.users.status.active": "Active", "// Role Badges": "", "admin.users.role.user": "User", "admin.users.role.developer": "Developer", "admin.users.role.admin": "Admin", "// User Details Modal": "", "// Action Menu": "", "admin.users.actions.viewDetails": "View Details", "admin.users.actions.disableAccount": "Disable Account", "admin.users.actions.enableAccount": "Enable Account", "admin.users.actions.resetPassword": "Reset Password", "admin.users.actions.exportData": "Export Data", "admin.users.details.title": "User Details", "admin.users.details.description": "View detailed information about this user", "admin.users.details.accountDetails": "Account Details", "admin.users.details.registered": "Registered", "admin.users.details.lastLogin": "Last Login", "admin.users.details.never": "Never", "admin.users.details.failedAttempts": "Failed Login Attempts", "admin.users.details.connectedApps": "Connected Applications", "admin.users.status.disabled": "Disabled", "admin.users.role.${role}": "admin users role [dynamic]"}