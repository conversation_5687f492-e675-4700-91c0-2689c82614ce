{"auth.signIn": "Sign in to continue", "auth.welcome": "Welcome to <PERSON><PERSON><PERSON><PERSON>", "auth.welcomeDescription": "Secure access to your account", "auth.username": "Username", "auth.password": "Password", "auth.usernamePlaceholder": "Enter your username", "auth.passwordPlaceholder": "Enter your password", "auth.login": "Sign In", "auth.loginAuthorize": "Sign In & Continue", "auth.authorizing": "Signing in...", "auth.invalidCredentials": "Invalid credentials. Please try again.", "auth.error": "An error occurred. Please try again.", "auth.poweredBy": "Powered by <PERSON><PERSON><PERSON>", "auth.secureAuth": "Secure Authentication", "auth.orContinueWith": "Or continue with", "auth.continueWithGoogle": "Continue with Google", "auth.quickLoginDemo": "<PERSON> Login for De<PERSON>", "auth.adminDemo": "<PERSON><PERSON>", "auth.developerDemo": "Developer Demo", "auth.returnToHome": "Return to Home", "auth.noAccount": "Don't have an account?", "auth.signUp": "Sign up", "auth.createAccount": "Create Account", "auth.registerToContinue": "Enter your details to create a new account", "auth.firstName": "First Name", "auth.lastName": "Last Name", "auth.email": "Email", "auth.passwordRequirement": "Password must be at least 8 characters", "auth.confirmPassword": "Confirm Password", "auth.haveAccount": "Already have an account?", "auth.error.firstNameRequired": "First name is required", "auth.error.lastNameRequired": "Last name is required", "auth.error.usernameRequired": "Username is required", "auth.error.emailRequired": "Email is required", "auth.error.passwordRequired": "Password is required", "auth.error.passwordTooShort": "Password must be at least 8 characters", "auth.error.passwordMismatch": "Passwords do not match", "auth.error.registrationFailed": "Registration failed. Please try again.", "auth.creatingAccount": "Creating account...", "auth.alreadyHaveAccount": "Already have an account?", "// Login Form Specific": "", "auth.loginForm.title": "GeNieGO SSO Login", "auth.loginForm.emailLabel": "Email", "auth.loginForm.emailPlaceholder": "Enter your email", "auth.loginForm.passwordLabel": "Password", "auth.loginForm.passwordPlaceholder": "Enter your password", "auth.loginForm.signingIn": "Signing in...", "auth.loginForm.signIn": "Sign In", "auth.loginForm.loginFailed": "<PERSON><PERSON> failed", "page.register.title": "Create Account", "page.register.description": "Enter your details to create a new account", "page.register.firstName": "First Name", "page.register.lastName": "Last Name", "page.register.email": "Email", "page.register.password": "Password", "page.register.submit": "Create Account", "page.register.haveAccount": "Already have an account? Sign in", "page.forgot.title": "Reset Password", "page.forgot.description": "Enter your email to receive a password reset link", "page.forgot.sendLink": "Send Reset Link", "page.forgot.success": "We've sent a password reset link to {email}", "page.forgot.backToLogin": "Back to login", "page.login.title": "<PERSON><PERSON>", "page.login.description": "Enter your email below to login to your account", "page.login.welcome": "Welcome back", "page.login.welcomeDescription": "Enter your credentials to access your account", "page.login.email": "Email", "page.login.password": "Password", "page.login.forgotPassword": "Forgot your password?", "page.login.submit": "<PERSON><PERSON>", "page.login.googleSignIn": "Google", "page.login.githubSignIn": "GitHub", "page.login.orContinueWith": "Or continue with", "page.login.noAccount": "Don't have an account?", "page.login.signUp": "Sign up", "page.login.error": "Invalid email or password. Please try again.", "page.login.testimonial.quote": "This app has saved me countless hours of work and helped me deliver stunning designs to my clients faster than ever before.", "page.login.testimonial.author": "<PERSON>", "// Logout Pages": "", "auth.logout.confirm.button": "Logout", "auth.logout.logging.out": "Logging out...", "auth.logout.slo.warning.title": "Single Logout (SLO)", "auth.logout.slo.warning.description": "This will log you out from all applications and devices using this SSO account.", "auth.logout.loading.sessions": "Loading session information...", "auth.logout.active.sessions": "Active Sessions", "auth.logout.no.active.sessions": "No active sessions found", "auth.logout.success.title": "Successfully Logged Out", "auth.logout.success.description": "You have been successfully logged out from all applications and devices.", "auth.logout.partial.title": "Logout Completed with Warnings", "auth.logout.partial.description": "You have been logged out, but some applications may not have been notified.", "auth.logout.statistics.title": "Logout Summary", "auth.logout.apps.notified": "Applications notified", "auth.logout.apps.failed": "Failed notifications", "auth.logout.security.notice.title": "Security Notice", "auth.logout.security.notice.description": "For security, close all browser windows and clear your browser cache if using a shared computer.", "auth.logout.auto.redirect": "Redirecting to login in {seconds} seconds...", "auth.logout.go.to.login": "Go to Login", "auth.logout.go.to.home": "Go to Home", "auth.logout.help.text": "Need help? Contact support for assistance.", "application": "auth application", "applicationRequestsAccess": "auth applicationRequestsAccess", "authorizationRequest": "auth authorizationRequest", "authorize": "auth authorize", "deny": "auth deny", "error.invalidRequest": "auth error invalidRequest", "error.missingParameters": "auth error missingParameters", "onlyAuthorizeAppsYouTrust": "auth onlyAuthorizeAppsYouTrust", "requestedPermissions": "auth requestedPermissions", "scope.email": "auth scope email", "scope.openid": "auth scope openid", "scope.profile": "auth scope profile", "securityNotice": "auth securityNotice", "signedInAs": "auth signedInAs"}