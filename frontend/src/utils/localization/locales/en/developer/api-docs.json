{"// Developer API Documentation": "", "developer.apiDocs.title": "API Documentation", "developer.apiDocs.description": "Complete API reference and integration guides for GeNieGO SSO", "// Getting Started": "", "// Authentication Guide": "", "// Code Examples": "", "// API Endpoints": "", "// SDKs": "", "// Support": "", "// Actions and Buttons": "", "developer.apiDocs.apiSettings": "API Settings", "developer.apiDocs.openApiSpec": "OpenAPI Spec", "developer.apiDocs.registerApp": "Register App", "developer.apiDocs.viewExamples": "View Examples", "developer.apiDocs.tryApi": "Try API", "// Quick Start Guide": "", "developer.apiDocs.quickStartGuide": "Quick Start Guide", "developer.apiDocs.quickStartDesc": "Get started with GeNieGO SSO API in minutes", "developer.apiDocs.getApiKeys": "1. Get API Keys", "developer.apiDocs.getApiKeysDesc": "Register your application and obtain client credentials", "developer.apiDocs.authenticate": "2. <PERSON><PERSON>nti<PERSON>", "developer.apiDocs.authenticateDesc": "Implement OAuth2 flow or use API key authentication", "developer.apiDocs.makeRequests": "3. Make Requests", "developer.apiDocs.makeRequestsDesc": "Start making API calls to manage users and applications", "// Categories": "", "// Toast Messages": "", "developer.apiDocs.toast.copied.title": "Copied to clipboard", "developer.apiDocs.toast.copied.description": "Code example has been copied to your clipboard.", "// Missing hardcoded text": "", "developer.apiDocs.searchPlaceholder": "Search endpoints...", "developer.apiDocs.authRequired": "Auth Required", "developer.apiDocs.codeExample": "Code Example", "developer.apiDocs.copy": "Copy", "developer.apiDocs.responses": "Responses", "developer.apiDocs.tryThisEndpoint": "Try This Endpoint"}