{"// Developer Dashboard": "", "developer.dashboard.title": "Developer Dashboard", "developer.dashboard.description": "Manage your applications and monitor user engagement with GeNieGO SSO", "developer.dashboard.refresh": "Refresh", "// Tabs": "", "developer.dashboard.tabs.overview": "Overview", "developer.dashboard.tabs.applications": "Applications", "developer.dashboard.tabs.analytics": "Analytics", "developer.dashboard.tabs.documentation": "Documentation", "// Quick Actions": "", "developer.dashboard.quickActions.registerApp": "Register New Application", "developer.dashboard.quickActions.registerAppDesc": "Add a new application to GeNieGO SSO", "developer.dashboard.quickActions.manageApps": "Manage Applications", "developer.dashboard.quickActions.manageAppsDesc": "View and configure your registered applications", "developer.dashboard.quickActions.apiDocs": "API Documentation", "developer.dashboard.quickActions.apiDocsDesc": "Integration guides and API reference", "developer.dashboard.quickActions.generateKeys": "Generate API Keys", "developer.dashboard.quickActions.generateKeysDesc": "Create and manage client credentials", "developer.dashboard.quickActions.viewAnalytics": "View Analytics", "developer.dashboard.quickActions.viewAnalyticsDesc": "Monitor usage and performance metrics", "developer.dashboard.quickActions.securitySettings": "Security Settings", "developer.dashboard.quickActions.securitySettingsDesc": "Configure security and compliance options", "// Overview Stats": "", "developer.dashboard.stats.myApplications": "My Applications", "developer.dashboard.stats.totalUsers": "Total Users", "developer.dashboard.stats.monthlyRequests": "Monthly Requests", "developer.dashboard.stats.apiCalls": "API Calls", "developer.dashboard.stats.active": "active", "developer.dashboard.stats.acrossAllApps": "Across all applications", "developer.dashboard.stats.thisMonth": "this month", "// Applications Tab": "", "developer.dashboard.applications.title": "My Applications", "developer.dashboard.applications.description": "Manage all your registered applications ({count} total)", "developer.dashboard.applications.filterTitle": "Filter Applications", "developer.dashboard.applications.searchPlaceholder": "Search applications by name or client ID...", "developer.dashboard.applications.manage": "Manage", "developer.dashboard.applications.viewAll": "View All {count} Applications", "developer.dashboard.applications.noAppsFound": "No Applications Found", "developer.dashboard.applications.noAppsFiltered": "No applications match your current filters", "developer.dashboard.applications.noAppsYet": "You haven't registered any applications yet", "developer.dashboard.applications.registerFirst": "Register Your First Application", "// Analytics Tab": "", "developer.dashboard.analytics.filterTitle": "Filter Analytics", "// Documentation Tab": "", "developer.dashboard.documentation.apiDocs": "API Documentation", "developer.dashboard.documentation.apiDocsDesc": "Complete API reference and guides", "developer.dashboard.documentation.viewDocs": "View Documentation", "developer.dashboard.documentation.apiKeys": "API Keys", "developer.dashboard.documentation.apiKeysDesc": "Manage your API keys and credentials", "developer.dashboard.documentation.manageKeys": "Manage Keys", "developer.dashboard.documentation.settings": "Settings", "developer.dashboard.documentation.settingsDesc": "Configure your developer account", "developer.dashboard.documentation.accountSettings": "Account <PERSON><PERSON>", "// Actions and Buttons": "", "developer.dashboard.newApplication": "New Application", "developer.dashboard.getStarted": "Get Started", "developer.dashboard.access": "Access", "// Status and States": "", "developer.dashboard.apiStatus": "API Status", "developer.dashboard.allServicesOperational": "All services operational", "developer.dashboard.online": "Online", "developer.dashboard.created": "Created", "developer.dashboard.active": "Active", "developer.dashboard.inactive": "Inactive", "// System Status": "", "developer.dashboard.systemStatus": "System Status", "developer.dashboard.systemStatusDesc": "Current status of GeNieGO SSO services", "developer.dashboard.systemStatus.authenticationApi": "Authentication API", "developer.dashboard.systemStatus.userManagement": "User Management", "developer.dashboard.systemStatus.tokenService": "Token Service", "developer.dashboard.systemStatus.analyticsEngine": "Analytics Engine", "developer.dashboard.systemStatus.uptime": "uptime", "developer.dashboard.systemStatus.${service.status}": "developer dashboard systemStatus [dynamic]"}