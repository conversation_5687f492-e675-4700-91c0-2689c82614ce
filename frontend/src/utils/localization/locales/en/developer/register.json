{"// Developer Pages": "", "developer.register.title": "Register New Application", "developer.register.subtitle": "Register your application with GeNieGO SSO to enable single sign-on for your users", "developer.register.basicInfo": "Basic Information", "developer.register.applicationName": "Application Name", "developer.register.applicationNamePlaceholder": "e.g., <PERSON><PERSON><PERSON><PERSON>, MyApp", "developer.register.description": "Description", "developer.register.descriptionPlaceholder": "Brief description of your application", "developer.register.redirectUris": "Redirect URIs", "developer.register.redirectUrisDescription": "Add the URLs where users will be redirected after authentication", "developer.register.redirectUriPlaceholder": "https://yourapp.com/auth/callback", "developer.register.addRedirectUri": "Add another redirect URI", "developer.register.permissions": "Permissions (<PERSON>opes)", "developer.register.permissionsDescription": "Select the user information your application needs to access", "developer.register.clientCredentials": "Client Credentials", "developer.register.credentialsDialogDescription": "Your application credentials have been generated. Please save them securely as the client secret will not be shown again.", "developer.register.securityNotice": "Important Security Notice", "developer.register.securityNoticeText": "Store these credentials securely. The client secret will only be shown once and cannot be recovered.", "developer.register.clientId": "Client ID", "developer.register.clientSecret": "Client Secret", "developer.register.downloadCSV": "Download CSV", "developer.register.close": "Close", "developer.register.termsAndConditions": "Terms and Conditions", "developer.register.agreeTo": "I agree to the", "developer.register.termsOfService": "Terms of Service", "developer.register.privacyPolicy": "Privacy Policy", "developer.register.cancel": "Cancel", "developer.register.agree": "Agree", "developer.register.registerApplication": "Register Application", "developer.register.registering": "Registering...", "developer.register.nextSteps": "Next Steps", "developer.register.nextStepsText": "After registration, you'll be able to integrate GeNieGO SSO into your application. Check out our integration documentation for implementation guides and code examples.", "developer.register.integrationDocs": "integration documentation", "developer.register.error.applicationNameRequired": "Application name is required", "developer.register.error.applicationNameTooShort": "Application name must be at least 3 characters", "developer.register.error.applicationNameInvalid": "Application name can only contain letters, numbers, spaces, hyphens, and underscores", "developer.register.error.descriptionRequired": "Description is required", "developer.register.error.descriptionTooShort": "Description must be at least 10 characters", "developer.register.error.redirectUriRequired": "At least one redirect URI is required", "developer.register.error.redirectUriInvalid": "All redirect URIs must be valid HTTP/HTTPS URLs", "developer.register.error.termsRequired": "You must accept the Terms of Service", "developer.register.error.privacyRequired": "You must accept the Privacy Policy", "// Toast Messages": "", "developer.register.toast.success.title": "Application Registered", "developer.register.toast.success.description": "{name} has been successfully registered.", "developer.register.toast.error.title": "Registration Failed", "developer.register.toast.error.description": "Failed to register application. Please try again.", "// Scope Options": "", "developer.register.scope.openid.name": "OpenID Connect", "developer.register.scope.openid.description": "Basic authentication", "developer.register.scope.profile.name": "Profile", "developer.register.scope.profile.description": "User profile information", "developer.register.scope.email.name": "Email", "developer.register.scope.email.description": "User email address", "developer.register.scope.phone.name": "Phone", "developer.register.scope.phone.description": "User phone number", "developer.register.scope.address.name": "Address", "developer.register.scope.address.description": "User address information"}