{"// Developer Settings": "", "developer.settings.title": "Developer Settings", "developer.settings.description": "Configure your developer account, API keys, and integration settings", "developer.settings.save": "Save Changes", "developer.settings.saving": "Saving...", "// Tabs": "", "developer.settings.tabs.general": "General", "developer.settings.tabs.apiKeys": "API Keys", "developer.settings.tabs.webhooks": "Webhooks", "developer.settings.tabs.notifications": "Notifications", "developer.settings.tabs.environments": "Environments", "// General Settings": "", "// API Keys": "", "developer.settings.apiKeys.title": "API Keys", "developer.settings.apiKeys.description": "Manage your API keys for accessing the GeNieGO API", "developer.settings.apiKeys.createNew": "Create New API Key", "developer.settings.apiKeys.permissions": "Permissions", "developer.settings.apiKeys.created": "Created", "developer.settings.apiKeys.expires": "Expires", "developer.settings.apiKeys.lastUsed": "Last Used", "developer.settings.apiKeys.active": "Active", "developer.settings.apiKeys.inactive": "Inactive", "developer.settings.apiKeys.never": "Never", "developer.settings.apiKeys.scopes": "scopes", "developer.settings.apiKeys.noKeys": "No API Keys", "developer.settings.apiKeys.noKeysLong": "You haven't generated any API keys yet. Create your first API key to start integrating with GeNieGO SSO.", "developer.settings.apiKeys.generateFirst": "Generate Your First API Key", "// Webhooks": "", "developer.settings.webhooks.title": "Webhooks", "developer.settings.webhooks.description": "Configure webhooks for real-time event notifications", "developer.settings.webhooks.createNew": "Create New Webhook", "// Security": "", "// Notifications": "", "// Environments": "", "// Toast Messages": "", "developer.settings.toast.copied": "Copied to clipboard", "developer.settings.toast.copiedDesc": "Text has been copied to your clipboard", "developer.settings.toast.saved": "Settings Saved", "developer.settings.toast.savedDesc": "Developer settings have been updated successfully", "developer.settings.toast.saveFailed": "Save Failed", "developer.settings.toast.saveFailedDesc": "Failed to save settings. Please try again", "developer.settings.toast.loadFailed": "Failed to Load Data", "developer.settings.toast.loadFailedDesc": "Unable to load API keys and webhooks. Please try again", "developer.settings.toast.settingsLoadFailed": "Failed to Load Settings", "developer.settings.toast.settingsLoadFailedDesc": "Unable to load developer settings. Please try again", "developer.settings.toast.comingSoon": "Feature Coming Soon", "developer.settings.toast.comingSoonDesc": "This feature will be available in a future update", "// Account Information": "", "developer.settings.accountInformation": "Account Information", "developer.settings.accountInformationDesc": "Update your developer account details", "developer.settings.companyName": "Company Name", "developer.settings.companyNamePlaceholder": "Your Company Name", "developer.settings.contactEmail": "Contact Email", "developer.settings.contactEmailPlaceholder": "<EMAIL>", "developer.settings.supportUrl": "Support URL", "developer.settings.supportUrlPlaceholder": "https://yourcompany.com/support", "// Development Environment": "", "developer.settings.developmentEnvironment": "Development Environment", "developer.settings.developmentEnvironmentDesc": "Configure your development and testing environment", "developer.settings.sandboxMode": "Sandbox Mode", "developer.settings.sandboxModeDesc": "Use test environment for development", "developer.settings.debugMode": "Debug Mode", "developer.settings.debugModeDesc": "Enable detailed API response logging", "developer.settings.rateLimitBypass": "Rate Limit Bypass", "developer.settings.rateLimitBypassDesc": "Bypass rate limits for testing (sandbox only)", "// Notification Preferences": "", "developer.settings.notificationPreferences": "Notification Preferences", "developer.settings.notificationPreferencesDesc": "Configure how you receive updates and alerts", "developer.settings.emailNotifications": "Email Notifications", "developer.settings.emailNotificationsDesc": "Receive general updates via email", "developer.settings.webhookFailureAlerts": "Webhook Failure Alerts", "developer.settings.webhookFailureAlertsDesc": "Get notified when webhooks fail", "developer.settings.usageAlerts": "<PERSON><PERSON>", "developer.settings.usageAlertsDesc": "Alerts for API usage limits", "developer.settings.securityAlerts": "Security Alerts", "developer.settings.securityAlertsDesc": "Important security notifications", "// Webhook Management": "", "developer.settings.noWebhooksConfigured": "No Webhooks Configured", "developer.settings.noWebhooksConfiguredDesc": "Set up webhook endpoints to receive real-time notifications about events in your applications.", "developer.settings.addFirstWebhook": "Add Your First Webhook", "developer.settings.webhookActive": "Active", "developer.settings.webhookInactive": "Inactive", "developer.settings.webhookCreated": "Created:", "developer.settings.webhookLastTriggered": "Last Triggered:", "developer.settings.webhookNever": "Never", "developer.settings.webhookEvents": "Events:", "developer.settings.webhookEventsSubscribed": "subscribed", "developer.settings.webhookSecret": "Secret:", "// API Configuration": "", "developer.settings.defaultApiVersion": "Default API Version", "developer.settings.requestTimeout": "Request Timeout (seconds)"}