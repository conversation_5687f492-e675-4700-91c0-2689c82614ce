{"profile": "Profile", "settings": "Settings", "security": "Security", "notifications": "Notifications", "avatar": "Avatar", "name": "Name", "email": "Email", "password": "Password", "phone": "Phone", "birthday": "Birthday", "gender": "Gender", "language": "Language", "timezone": "Timezone", "theme": "Theme", "privacy": "Privacy", "preferences": "Preferences", "status": "Status", "role": "Role", "permissions": "Permissions", "subscription": "Subscription", "billing": "Billing", "payment": "Payment", "api": "API", "tokens": "Tokens", "sessions": "Sessions", "history": "History", "logout": "Logout", "delete": "Delete", "deactivate": "Deactivate", "save": "Save", "cancel": "Cancel", "edit": "Edit", "update": "Update", "change": "Change", "verify": "Verify", "confirm": "Confirm", "reset": "Reset", "enable": "Enable", "disable": "Disable", "show": "Show", "hide": "<PERSON>de", "upload": "Upload", "download": "Download", "export": "Export", "import": "Import", "consent.back": "user consent back", "consent.deny": "user consent deny", "consent.grant": "user consent grant", "consent.scope.email.description": "user consent scope email description", "consent.scope.email.title": "user consent scope email title", "consent.scope.openid.description": "user consent scope openid description", "consent.scope.openid.title": "user consent scope openid title", "consent.scope.profile.description": "user consent scope profile description", "consent.scope.profile.title": "user consent scope profile title", "consent.scope.read.description": "user consent scope read description", "consent.scope.read.title": "user consent scope read title", "consent.scope.write.description": "user consent scope write description", "consent.scope.write.title": "user consent scope write title", "role.transition.status.${application.status}": "user role transition status [dynamic]", "sessions.expiring": "user sessions expiring", "twofa.setup.subtitle": "user twofa setup subtitle", "twofa.setup.title": "user twofa setup title"}