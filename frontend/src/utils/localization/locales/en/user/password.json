{"// User Password Management Page": "", "user.password.title": "Password Management", "user.password.subtitle": "Change your password and manage security settings", "// Form Labels and Placeholders": "", "user.password.changePassword": "Change Password", "user.password.changePassword.description": "Update your password to keep your account secure", "user.password.currentPassword": "Current Password", "user.password.currentPassword.placeholder": "Enter your current password", "user.password.newPassword": "New Password", "user.password.newPassword.placeholder": "Enter your new password", "user.password.confirmPassword": "Confirm New Password", "user.password.confirmPassword.placeholder": "Confirm your new password", "user.password.updating": "Updating...", "user.password.updatePassword": "Update Password", "// Error Messages": "", "user.password.error.currentRequired": "Current password is required", "user.password.error.newRequired": "New password is required", "user.password.error.minLength": "Password must be at least 8 characters long", "user.password.error.confirmRequired": "Please confirm your new password", "user.password.error.passwordMismatch": "Passwords do not match", "user.password.error.samePassword": "New password must be different from current password", "// Success Messages": "", "user.password.success.title": "Password Updated", "user.password.success.description": "Your password has been successfully changed.", "user.password.error.title": "Update Failed", "user.password.error.description": "Failed to update password. Please try again.", "// Password Strength": "", "user.password.strength.label": "Password Strength:", "user.password.strength.weak": "Weak", "user.password.strength.fair": "Fair", "user.password.strength.good": "Good", "user.password.strength.strong": "Strong", "// Password Requirements": "", "user.password.requirements.title": "Password Requirements", "user.password.requirements.length": "At least 8 characters", "user.password.requirements.lowercase": "Lowercase letter (a-z)", "user.password.requirements.uppercase": "Uppercase letter (A-Z)", "user.password.requirements.numbers": "Number (0-9)", "user.password.requirements.symbols": "Special character (!@#$%^&*)", "user.password.requirements.noCommon": "Not a common password", "user.password.requirements.info.length": "At least 8 characters", "user.password.requirements.info.case": "Mix of uppercase and lowercase", "user.password.requirements.info.numbers": "Include numbers and symbols", "user.password.requirements.info.common": "Avoid common passwords", "// Security Information": "", "user.password.security.title": "Security Information", "user.password.security.lastChanged": "Last Password Change", "user.password.security.never": "Never", "user.password.security.daysAgo": "{days} days ago", "user.password.security.dayAgo": "1 day ago", "user.password.security.monthsAgo": "{months} months ago", "user.password.security.yearsAgo": "{years} years ago", "user.password.security.twoFactor": "Two-Factor Authentication", "user.password.security.enabled": "Enabled", "user.password.security.notEnabled": "Not enabled", "user.password.security.activeSessions": "Active Sessions", "user.password.security.devicesSignedIn": "devices signed in", "// Security Tips": "", "user.password.tips.title": "Security Tips:", "user.password.tips.unique": "Use a unique password for this account", "user.password.tips.manager": "Consider using a password manager", "user.password.tips.twoFactor": "Enable two-factor authentication", "user.password.tips.regular": "Change passwords regularly"}