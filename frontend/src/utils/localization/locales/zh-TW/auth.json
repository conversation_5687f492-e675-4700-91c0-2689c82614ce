{"auth.signIn": "登錄以繼續", "auth.welcome": "歡迎來到一元樂動", "auth.welcomeDescription": "安全訪問您的帳戶", "auth.username": "用戶名", "auth.password": "密碼", "auth.usernamePlaceholder": "請輸入用戶名", "auth.passwordPlaceholder": "請輸入密碼", "auth.login": "登錄", "auth.loginAuthorize": "登錄並繼續", "auth.authorizing": "登錄中...", "auth.invalidCredentials": "憑據無效，請重試。", "auth.error": "發生錯誤，請重試。", "auth.poweredBy": "一元精靈版權所有", "auth.secureAuth": "安全認證", "auth.orContinueWith": "或繼續使用", "auth.continueWithGoogle": "使用 Google 繼續", "auth.quickLoginDemo": "快速登錄演示", "auth.adminDemo": "管理員演示", "auth.developerDemo": "開發者演示", "auth.returnToHome": "返回首頁", "auth.noAccount": "沒有帳戶？", "auth.signUp": "註冊", "auth.createAccount": "創建帳戶", "auth.registerToContinue": "請輸入您的詳細資訊以創建新帳戶", "auth.firstName": "名字", "auth.lastName": "姓氏", "auth.email": "電子郵件", "auth.passwordRequirement": "密碼必須至少8個字符", "auth.confirmPassword": "確認密碼", "auth.haveAccount": "已有帳戶？", "auth.error.firstNameRequired": "名字是必填項", "auth.error.lastNameRequired": "姓氏是必填項", "auth.error.usernameRequired": "用戶名是必填項", "auth.error.emailRequired": "電子郵件是必填項", "auth.error.passwordRequired": "密碼是必填項", "auth.error.passwordTooShort": "密碼必須至少8個字符", "auth.error.passwordMismatch": "密碼不匹配", "auth.error.registrationFailed": "註冊失敗，請重試。", "auth.creatingAccount": "創建帳戶中...", "auth.alreadyHaveAccount": "已有帳戶？", "// Login Form Specific": "", "auth.loginForm.title": "GeNieGO SSO 登入", "auth.loginForm.emailLabel": "電子郵件", "auth.loginForm.emailPlaceholder": "輸入您的電子郵件", "auth.loginForm.passwordLabel": "密碼", "auth.loginForm.passwordPlaceholder": "輸入您的密碼", "auth.loginForm.signingIn": "登入中...", "auth.loginForm.signIn": "登入", "auth.loginForm.loginFailed": "登入失敗", "page.register.title": "創建帳戶", "page.register.description": "請輸入您的詳細資訊以創建新帳戶", "page.register.firstName": "名字", "page.register.lastName": "姓氏", "page.register.email": "電子郵件", "page.register.password": "密碼", "page.register.submit": "創建帳戶", "page.register.haveAccount": "已有帳戶？登入", "page.forgot.title": "重設密碼", "page.forgot.description": "輸入您的電子郵件以接收密碼重設連結", "page.forgot.sendLink": "發送重設連結", "page.forgot.success": "我們已向 {email} 發送了密碼重設連結", "page.forgot.backToLogin": "返回登入", "page.login.title": "登入", "page.login.description": "輸入您的電子郵件以登入您的帳戶", "page.login.welcome": "歡迎回來", "page.login.welcomeDescription": "輸入您的憑據以訪問您的帳戶", "page.login.email": "電子郵件", "page.login.password": "密碼", "page.login.forgotPassword": "忘記密碼？", "page.login.submit": "登入", "page.login.googleSignIn": "谷歌", "page.login.githubSignIn": "GitHub", "page.login.orContinueWith": "或繼續使用", "page.login.noAccount": "沒有帳戶？", "page.login.signUp": "註冊", "page.login.error": "電子郵件或密碼無效。請重試。", "page.login.testimonial.quote": "這個應用程式為我節省了無數小時的工作時間，幫助我比以往更快地為客戶提供出色的設計。", "page.login.testimonial.author": "索菲亞·戴維斯", "// 登出頁面": "", "auth.logout.confirm.button": "登出", "auth.logout.logging.out": "正在登出...", "auth.logout.slo.warning.title": "單一登出 (SLO)", "auth.logout.slo.warning.description": "這將使您從所有使用此 SSO 帳戶的應用程式和設備登出。", "auth.logout.loading.sessions": "正在載入會話資訊...", "auth.logout.active.sessions": "活躍會話", "auth.logout.no.active.sessions": "未找到活躍會話", "auth.logout.success.title": "成功登出", "auth.logout.success.description": "您已成功從所有應用程式和設備登出。", "auth.logout.partial.title": "登出完成但有警告", "auth.logout.partial.description": "您已登出，但某些應用程式可能未收到通知。", "auth.logout.statistics.title": "登出摘要", "auth.logout.apps.notified": "已通知的應用程式", "auth.logout.apps.failed": "通知失敗", "auth.logout.security.notice.title": "安全提醒", "auth.logout.security.notice.description": "為了安全起見，如果使用共用電腦，請關閉所有瀏覽器視窗並清除瀏覽器快取。", "auth.logout.auto.redirect": "將在 {seconds} 秒後重定向到登入頁面...", "application": "[TODO: Translate] auth.application", "applicationRequestsAccess": "[TODO: Translate] auth.applicationRequestsAccess", "authorizationRequest": "[TODO: Translate] auth.authorizationRequest", "authorize": "[TODO: Translate] auth.authorize", "deny": "[TODO: Translate] auth.deny", "error.invalidRequest": "[TODO: Translate] auth.error.invalidRequest", "error.missingParameters": "[TODO: Translate] auth.error.missingParameters", "onlyAuthorizeAppsYouTrust": "[TODO: Translate] auth.onlyAuthorizeAppsYouTrust", "requestedPermissions": "[TODO: Translate] auth.requestedPermissions", "scope.email": "[TODO: Translate] auth.scope.email", "scope.openid": "[TODO: Translate] auth.scope.openid", "scope.profile": "[TODO: Translate] auth.scope.profile", "securityNotice": "[TODO: Translate] auth.securityNotice", "signedInAs": "[TODO: Translate] auth.signedInAs"}