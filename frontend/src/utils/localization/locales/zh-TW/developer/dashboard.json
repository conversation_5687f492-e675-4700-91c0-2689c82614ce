{"// Developer Dashboard": "", "developer.dashboard.title": "開發者儀表板", "developer.dashboard.description": "管理您的應用程式並監控 GeNieGO SSO 的使用者參與度", "developer.dashboard.refresh": "重新整理", "// Tabs": "", "developer.dashboard.tabs.overview": "概覽", "developer.dashboard.tabs.applications": "應用程式", "developer.dashboard.tabs.analytics": "分析", "developer.dashboard.tabs.documentation": "文件", "// Quick Actions": "", "developer.dashboard.quickActions.registerApp": "註冊新應用程式", "developer.dashboard.quickActions.registerAppDesc": "將新應用程式新增到 GeNieGO SSO", "developer.dashboard.quickActions.manageApps": "管理應用程式", "developer.dashboard.quickActions.manageAppsDesc": "檢視和配置您已註冊的應用程式", "developer.dashboard.quickActions.apiDocs": "API 文件", "developer.dashboard.quickActions.apiDocsDesc": "整合指南和 API 參考", "developer.dashboard.quickActions.generateKeys": "產生 API 金鑰", "developer.dashboard.quickActions.generateKeysDesc": "建立和管理客戶端憑證", "developer.dashboard.quickActions.viewAnalytics": "檢視分析", "developer.dashboard.quickActions.viewAnalyticsDesc": "監控使用情況和效能指標", "developer.dashboard.quickActions.securitySettings": "安全設定", "developer.dashboard.quickActions.securitySettingsDesc": "配置安全性和合規選項", "// Overview Stats": "", "developer.dashboard.stats.myApplications": "我的應用程式", "developer.dashboard.stats.totalUsers": "使用者總數", "developer.dashboard.stats.monthlyRequests": "月度請求", "developer.dashboard.stats.apiCalls": "API 呼叫", "developer.dashboard.stats.active": "活躍", "developer.dashboard.stats.acrossAllApps": "跨所有應用程式", "developer.dashboard.stats.thisMonth": "本月", "// Applications Tab": "", "developer.dashboard.applications.title": "我的應用程式", "developer.dashboard.applications.description": "管理您所有已註冊的應用程式（共 {count} 個）", "developer.dashboard.applications.filterTitle": "篩選應用程式", "developer.dashboard.applications.searchPlaceholder": "按名稱或客戶端 ID 搜尋應用程式...", "developer.dashboard.applications.manage": "管理", "developer.dashboard.applications.viewAll": "檢視所有 {count} 個應用程式", "developer.dashboard.applications.noAppsFound": "找不到應用程式", "developer.dashboard.applications.noAppsFiltered": "沒有應用程式符合您目前的篩選條件", "developer.dashboard.applications.noAppsYet": "您尚未註冊任何應用程式", "developer.dashboard.applications.registerFirst": "註冊您的第一個應用程式", "// Analytics Tab": "", "developer.dashboard.analytics.filterTitle": "篩選分析", "// Documentation Tab": "", "developer.dashboard.documentation.apiDocs": "API 文件", "developer.dashboard.documentation.apiDocsDesc": "完整的 API 參考和指南", "developer.dashboard.documentation.viewDocs": "檢視文件", "developer.dashboard.documentation.integrationGuides": "整合指南", "developer.dashboard.documentation.integrationGuidesDesc": "逐步整合教學", "developer.dashboard.documentation.codeExamples": "程式碼範例", "developer.dashboard.documentation.codeExamplesDesc": "即用型程式碼片段和範例", "developer.dashboard.documentation.apiKeys": "API 金鑰", "developer.dashboard.documentation.apiKeysDesc": "管理您的 API 金鑰和憑證", "developer.dashboard.documentation.manageKeys": "管理金鑰", "developer.dashboard.documentation.settings": "設定", "developer.dashboard.documentation.settingsDesc": "配置您的開發者帳戶", "developer.dashboard.documentation.accountSettings": "帳戶設定", "// Actions and Buttons": "", "developer.dashboard.newApplication": "新應用程式", "developer.dashboard.getStarted": "開始使用", "developer.dashboard.access": "存取", "// Status and States": "", "developer.dashboard.apiStatus": "API 狀態", "developer.dashboard.allServicesOperational": "所有服務正常運行", "developer.dashboard.online": "線上", "developer.dashboard.created": "建立時間", "developer.dashboard.active": "啟用", "developer.dashboard.inactive": "停用", "// System Status": "", "developer.dashboard.systemStatus": "繫統狀態", "developer.dashboard.systemStatusDesc": "GeNieGO SSO 服務的當前狀態", "developer.dashboard.systemStatus.authenticationApi": "身份驗證 API", "developer.dashboard.systemStatus.userManagement": "使用者管理", "developer.dashboard.systemStatus.tokenService": "權杖服務", "developer.dashboard.systemStatus.analyticsEngine": "分析引擎", "developer.dashboard.systemStatus.operational": "正常運行", "developer.dashboard.systemStatus.maintenance": "維護中", "developer.dashboard.systemStatus.down": "停機", "developer.dashboard.systemStatus.uptime": "正常運行時間", "developer.dashboard.systemStatus.${service.status}": "[TODO: Translate] developer.dashboard.systemStatus.${service.status}"}