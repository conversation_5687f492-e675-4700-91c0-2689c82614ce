{"auth.signIn": "登录以继续", "auth.welcome": "欢迎来到一元乐动", "auth.welcomeDescription": "安全访问您的账户", "auth.username": "用户名", "auth.password": "密码", "auth.usernamePlaceholder": "请输入用户名", "auth.passwordPlaceholder": "请输入密码", "auth.login": "登录", "auth.loginAuthorize": "登录并继续", "auth.authorizing": "登录中...", "auth.invalidCredentials": "凭据无效，请重试。", "auth.error": "发生错误，请重试。", "auth.poweredBy": "一元精靈版权所有", "auth.secureAuth": "安全认证", "auth.orContinueWith": "或继续使用", "auth.continueWithGoogle": "使用 Google 继续", "auth.quickLoginDemo": "快速登录演示", "auth.adminDemo": "管理员演示", "auth.developerDemo": "开发者演示", "auth.returnToHome": "返回首页", "auth.noAccount": "没有账户？", "auth.signUp": "注册", "auth.createAccount": "创建账户", "auth.registerToContinue": "请输入您的详细资讯以创建新账户", "auth.firstName": "名字", "auth.lastName": "姓氏", "auth.email": "电子邮件", "auth.passwordRequirement": "密码必须至少8个字符", "auth.confirmPassword": "确认密码", "auth.haveAccount": "已有账户？", "auth.error.firstNameRequired": "名字是必填项", "auth.error.lastNameRequired": "姓氏是必填项", "auth.error.usernameRequired": "用户名是必填项", "auth.error.emailRequired": "电子邮件是必填项", "auth.error.passwordRequired": "密码是必填项", "auth.error.passwordTooShort": "密码必须至少8个字符", "auth.error.passwordMismatch": "密码不匹配", "auth.error.registrationFailed": "注册失败，请重试。", "auth.creatingAccount": "创建账户中...", "auth.alreadyHaveAccount": "已有账户？", "// Login Form Specific": "", "auth.loginForm.title": "GeNieGO SSO 登入", "auth.loginForm.emailLabel": "电子邮件", "auth.loginForm.emailPlaceholder": "输入您的电子邮件", "auth.loginForm.passwordLabel": "密码", "auth.loginForm.passwordPlaceholder": "输入您的密码", "auth.loginForm.signingIn": "登入中...", "auth.loginForm.signIn": "登入", "auth.loginForm.loginFailed": "登入失败", "page.register.title": "创建账户", "page.register.description": "请输入您的详细资讯以创建新账户", "page.register.firstName": "名字", "page.register.lastName": "姓氏", "page.register.email": "电子邮件", "page.register.password": "密码", "page.register.submit": "创建账户", "page.register.haveAccount": "已有账户？登入", "page.forgot.title": "重设密码", "page.forgot.description": "输入您的电子邮件以接收密码重设连结", "page.forgot.sendLink": "发送重设连结", "page.forgot.success": "我们已向 {email} 发送了密码重设连结", "page.forgot.backToLogin": "返回登入", "page.login.title": "登入", "page.login.description": "输入您的电子邮件以登入您的账户", "page.login.welcome": "欢迎回来", "page.login.welcomeDescription": "输入您的凭据以访问您的账户", "page.login.email": "电子邮件", "page.login.password": "密码", "page.login.forgotPassword": "忘记密码？", "page.login.submit": "登入", "page.login.googleSignIn": "谷歌", "page.login.githubSignIn": "GitHub", "page.login.orContinueWith": "或继续使用", "page.login.noAccount": "没有账户？", "page.login.signUp": "注册", "page.login.error": "电子邮件或密码无效。请重试。", "page.login.testimonial.quote": "这个应用程式为我节省了无数小时的工作时间，帮助我比以往更快地为客户提供出色的设计。", "page.login.testimonial.author": "索菲亚·戴维斯", "// 登出頁面": "", "auth.logout.confirm.button": "登出", "auth.logout.logging.out": "正在登出...", "auth.logout.slo.warning.title": "单一登出 (SLO)", "auth.logout.slo.warning.description": "这将使您从所有使用此 SSO 账户的应用程式和设备登出。", "auth.logout.loading.sessions": "正在载入会话资讯...", "auth.logout.active.sessions": "活跃会话", "auth.logout.no.active.sessions": "未找到活跃会话", "auth.logout.success.title": "成功登出", "auth.logout.success.description": "您已成功从所有应用程式和设备登出。", "auth.logout.partial.title": "登出完成但有警告", "auth.logout.partial.description": "您已登出，但某些应用程式可能未收到通知。", "auth.logout.statistics.title": "登出摘要", "auth.logout.apps.notified": "已通知的应用程式", "auth.logout.apps.failed": "通知失败", "auth.logout.security.notice.title": "安全提醒", "auth.logout.security.notice.description": "为了安全起见，如果使用共用电脑，请关闭所有浏览器视窗并清除浏览器快取。", "auth.logout.auto.redirect": "将在 {seconds} 秒后重定向到登入页面...", "application": "[TODO: Translate] auth.application", "applicationRequestsAccess": "[TODO: Translate] auth.applicationRequestsAccess", "authorizationRequest": "[TODO: Translate] auth.authorizationRequest", "authorize": "[TODO: Translate] auth.authorize", "deny": "[TODO: Translate] auth.deny", "error.invalidRequest": "[TODO: Translate] auth.error.invalidRequest", "error.missingParameters": "[TODO: Translate] auth.error.missingParameters", "onlyAuthorizeAppsYouTrust": "[TODO: Translate] auth.onlyAuthorizeAppsYouTrust", "requestedPermissions": "[TODO: Translate] auth.requestedPermissions", "scope.email": "[TODO: Translate] auth.scope.email", "scope.openid": "[TODO: Translate] auth.scope.openid", "scope.profile": "[TODO: Translate] auth.scope.profile", "securityNotice": "[TODO: Translate] auth.securityNotice", "signedInAs": "[TODO: Translate] auth.signedInAs"}