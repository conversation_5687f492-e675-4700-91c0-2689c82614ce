{"// Developer Dashboard": "", "developer.dashboard.title": "开发者儀表板", "developer.dashboard.description": "管理您的应用程式并監控 GeNieGO SSO 的使用者參與度", "developer.dashboard.refresh": "重新整理", "// Tabs": "", "developer.dashboard.tabs.overview": "概览", "developer.dashboard.tabs.applications": "应用程式", "developer.dashboard.tabs.analytics": "分析", "developer.dashboard.tabs.documentation": "文件", "// Quick Actions": "", "developer.dashboard.quickActions.registerApp": "注册新应用程式", "developer.dashboard.quickActions.registerAppDesc": "将新应用程式新增到 GeNieGO SSO", "developer.dashboard.quickActions.manageApps": "管理应用程式", "developer.dashboard.quickActions.manageAppsDesc": "檢视和配置您已注册的应用程式", "developer.dashboard.quickActions.apiDocs": "API 文件", "developer.dashboard.quickActions.apiDocsDesc": "整合指南和 API 參考", "developer.dashboard.quickActions.generateKeys": "產生 API 金鑰", "developer.dashboard.quickActions.generateKeysDesc": "建立和管理客户端凭证", "developer.dashboard.quickActions.viewAnalytics": "檢视分析", "developer.dashboard.quickActions.viewAnalyticsDesc": "監控使用情況和效能指标", "developer.dashboard.quickActions.securitySettings": "安全设定", "developer.dashboard.quickActions.securitySettingsDesc": "配置安全性和合規選项", "// Overview Stats": "", "developer.dashboard.stats.myApplications": "我的应用程式", "developer.dashboard.stats.totalUsers": "使用者總数", "developer.dashboard.stats.monthlyRequests": "月度请求", "developer.dashboard.stats.apiCalls": "API 呼叫", "developer.dashboard.stats.active": "活跃", "developer.dashboard.stats.acrossAllApps": "跨所有应用程式", "developer.dashboard.stats.thisMonth": "本月", "// Applications Tab": "", "developer.dashboard.applications.title": "我的应用程式", "developer.dashboard.applications.description": "管理您所有已注册的应用程式（共 {count} 个）", "developer.dashboard.applications.filterTitle": "篩選应用程式", "developer.dashboard.applications.searchPlaceholder": "按名稱或客户端 ID 搜尋应用程式...", "developer.dashboard.applications.manage": "管理", "developer.dashboard.applications.viewAll": "檢视所有 {count} 个应用程式", "developer.dashboard.applications.noAppsFound": "找不到应用程式", "developer.dashboard.applications.noAppsFiltered": "没有应用程式符合您目前的篩選條件", "developer.dashboard.applications.noAppsYet": "您尚未注册任何应用程式", "developer.dashboard.applications.registerFirst": "注册您的第一个应用程式", "// Analytics Tab": "", "developer.dashboard.analytics.filterTitle": "篩選分析", "// Documentation Tab": "", "developer.dashboard.documentation.apiDocs": "API 文件", "developer.dashboard.documentation.apiDocsDesc": "完整的 API 參考和指南", "developer.dashboard.documentation.viewDocs": "檢视文件", "developer.dashboard.documentation.integrationGuides": "整合指南", "developer.dashboard.documentation.integrationGuidesDesc": "逐步整合教學", "developer.dashboard.documentation.codeExamples": "程式码範例", "developer.dashboard.documentation.codeExamplesDesc": "即用型程式码片段和範例", "developer.dashboard.documentation.apiKeys": "API 金鑰", "developer.dashboard.documentation.apiKeysDesc": "管理您的 API 金鑰和凭证", "developer.dashboard.documentation.manageKeys": "管理金鑰", "developer.dashboard.documentation.settings": "设定", "developer.dashboard.documentation.settingsDesc": "配置您的开发者账户", "developer.dashboard.documentation.accountSettings": "账户设定", "// Actions and Buttons": "", "developer.dashboard.newApplication": "新应用程式", "developer.dashboard.getStarted": "开始使用", "developer.dashboard.access": "存取", "// Status and States": "", "developer.dashboard.apiStatus": "API 状态", "developer.dashboard.allServicesOperational": "所有服務正常運行", "developer.dashboard.online": "線上", "developer.dashboard.created": "建立时间", "developer.dashboard.active": "啟用", "developer.dashboard.inactive": "停用", "// System Status": "", "developer.dashboard.systemStatus": "系統状态", "developer.dashboard.systemStatusDesc": "GeNieGO SSO 服務的當前状态", "developer.dashboard.systemStatus.authenticationApi": "身份驗证 API", "developer.dashboard.systemStatus.userManagement": "使用者管理", "developer.dashboard.systemStatus.tokenService": "权杖服務", "developer.dashboard.systemStatus.analyticsEngine": "分析引擎", "developer.dashboard.systemStatus.operational": "正常運行", "developer.dashboard.systemStatus.maintenance": "维護中", "developer.dashboard.systemStatus.down": "停機", "developer.dashboard.systemStatus.uptime": "正常運行时间", "developer.dashboard.systemStatus.${service.status}": "[TODO: Translate] developer.dashboard.systemStatus.${service.status}"}