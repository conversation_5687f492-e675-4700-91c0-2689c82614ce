#!/bin/bash

# Add Missing Translation Keys Script
# Adds all missing keys to appropriate JSON files in all languages

set -euo pipefail

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
LOCALES_DIR="$PROJECT_ROOT/frontend/src/utils/localization/locales"
MISSING_KEYS_FILE="/tmp/missing_keys.txt"

echo "➕ Adding Missing Translation Keys"
echo "================================="

# Function to determine the correct JSON file for a key
get_target_file() {
    local lang="$1"
    local key="$2"
    local lang_dir="$LOCALES_DIR/$lang"
    
    # Handle special cases and nested keys
    case "$key" in
        admin.*)
            if [[ "$key" == admin.applications.* ]]; then
                echo "$lang_dir/admin/applications.json"
            elif [[ "$key" == admin.developer-applications.* ]]; then
                echo "$lang_dir/admin/developer-applications.json"
            elif [[ "$key" == admin.security.* ]]; then
                echo "$lang_dir/admin/security.json"
            elif [[ "$key" == admin.users.* ]]; then
                echo "$lang_dir/admin/users.json"
            else
                echo "$lang_dir/admin.json"
            fi
            ;;
        auth.*)
            echo "$lang_dir/auth.json"
            ;;
        blog.*)
            echo "$lang_dir/blog.json"
            ;;
        common.*)
            echo "$lang_dir/common.json"
            ;;
        developer.*)
            if [[ "$key" == developer.dashboard.* ]]; then
                echo "$lang_dir/developer/dashboard.json"
            elif [[ "$key" == developer.organizations.* ]]; then
                echo "$lang_dir/developer/organizations.json"
            else
                echo "$lang_dir/developer.json"
            fi
            ;;
        nav.*)
            echo "$lang_dir/nav.json"
            ;;
        page.*)
            echo "$lang_dir/page.json"
            ;;
        permission.*)
            echo "$lang_dir/permission.json"
            ;;
        session.*)
            echo "$lang_dir/session.json"
            ;;
        time.*)
            echo "$lang_dir/time.json"
            ;;
        user.*)
            echo "$lang_dir/user.json"
            ;;
        *)
            echo "$lang_dir/common.json"
            ;;
    esac
}

# Function to get the key name within the file (remove prefix if needed)
get_key_name() {
    local key="$1"
    local target_file="$2"
    
    # Extract the base filename without extension
    local file_base=$(basename "$target_file" .json)
    
    # If the key starts with the file base, remove that prefix
    if [[ "$key" == "$file_base."* ]]; then
        echo "${key#$file_base.}"
    else
        echo "$key"
    fi
}

# Function to add a key to a JSON file
add_key_to_file() {
    local target_file="$1"
    local key_name="$2"
    local key_value="$3"
    local lang="$4"
    
    # Create directory if it doesn't exist
    mkdir -p "$(dirname "$target_file")"
    
    # Create file if it doesn't exist
    if [[ ! -f "$target_file" ]]; then
        echo "{}" > "$target_file"
    fi
    
    # Check if key already exists
    if jq -e --arg key "$key_name" 'has($key)' "$target_file" >/dev/null 2>&1; then
        echo "    ⚠️  Key '$key_name' already exists in $(basename "$target_file")"
        return
    fi
    
    # Add the key using jq
    local temp_file=$(mktemp)
    if jq --arg key "$key_name" --arg value "$key_value" '. + {($key): $value}' "$target_file" > "$temp_file"; then
        mv "$temp_file" "$target_file"
        echo "    ✅ Added '$key_name' to $(basename "$target_file")"
    else
        echo "    ❌ Failed to add '$key_name' to $(basename "$target_file")"
        rm -f "$temp_file"
    fi
}

# Function to add missing keys to a specific language
add_keys_to_language() {
    local lang="$1"
    local lang_dir="$LOCALES_DIR/$lang"
    
    echo "🔧 Adding missing keys to language: $lang"
    
    if [[ ! -f "$MISSING_KEYS_FILE" ]]; then
        echo "  ❌ Missing keys file not found: $MISSING_KEYS_FILE"
        return 1
    fi
    
    local added_count=0
    local skipped_count=0
    
    while IFS= read -r key; do
        [[ -z "$key" ]] && continue
        
        # Get target file and key name
        local target_file=$(get_target_file "$lang" "$key")
        local key_name=$(get_key_name "$key" "$target_file")
        
        # Determine the value based on language
        local key_value
        if [[ "$lang" == "en" ]]; then
            # For English, create a human-readable value
            key_value=$(echo "$key" | sed 's/\./\ /g' | sed 's/\${[^}]*}/[dynamic]/g')
        else
            # For other languages, use a translation placeholder
            key_value="[TODO: Translate] $key"
        fi
        
        echo "  ➕ Processing: $key"
        add_key_to_file "$target_file" "$key_name" "$key_value" "$lang"
        
        ((added_count++))
        
    done < "$MISSING_KEYS_FILE"
    
    echo "  📊 Processed $added_count keys for $lang"
}

# Main execution
echo "📋 Starting missing key addition process..."
echo

# Check if missing keys file exists
if [[ ! -f "$MISSING_KEYS_FILE" ]]; then
    echo "🔍 Extracting missing keys..."
    cd "$PROJECT_ROOT"
    ./scripts/dev/translation-key-manager.sh missing | grep "WARNING" | grep " - " | sed 's/.*- //' > "$MISSING_KEYS_FILE"
    echo "  📊 Extracted $(wc -l < "$MISSING_KEYS_FILE") missing keys"
fi

# Add keys to each language
for lang in en zh zh-TW; do
    if [[ -d "$LOCALES_DIR/$lang" ]]; then
        add_keys_to_language "$lang"
        echo
    fi
done

echo "🔍 Verifying results..."
cd "$PROJECT_ROOT"
echo "📊 Missing keys after addition:"
./scripts/dev/translation-key-manager.sh missing | grep "Found.*missing keys" || echo "✅ No missing keys found!"

echo
echo "🎉 Missing key addition completed!"
