#!/bin/bash

# Efficient batch removal of all unused translation keys

set -euo pipefail

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
LOCALES_DIR="$PROJECT_ROOT/frontend/src/utils/localization/locales"

echo "🧹 Batch Removal of All Unused Translation Keys"
echo "=================================================="

# Function to get unused keys for a language
get_unused_keys() {
    local lang="$1"
    cd "$PROJECT_ROOT"
    ./scripts/dev/translation-key-manager.sh unused 2>/dev/null | \
        grep -A 10000 "Found .* unused keys in language: $lang" | \
        grep "^\\[WARNING\\]   - " | \
        sed 's/^\\[WARNING\\]   - //' | \
        head -n -1  # Remove the last line which is usually "... and X more"
}

# Function to remove keys efficiently from a language
remove_keys_from_language() {
    local lang="$1"
    local lang_dir="$LOCALES_DIR/$lang"
    
    echo "🔧 Processing language: $lang"
    
    # Get unused keys for this language
    local unused_keys=()
    while IFS= read -r key; do
        [[ -n "$key" ]] && unused_keys+=("$key")
    done < <(get_unused_keys "$lang")
    
    if [[ ${#unused_keys[@]} -eq 0 ]]; then
        echo "  ✅ No unused keys found for $lang"
        return
    fi
    
    echo "  📊 Found ${#unused_keys[@]} unused keys to remove"
    
    # Process each JSON file and remove all unused keys from it
    local total_removed=0
    
    # Get all JSON files
    local json_files=()
    while IFS= read -r -d '' json_file; do
        json_files+=("$json_file")
    done < <(find "$lang_dir" -name "*.json" -type f -print0)
    
    for json_file in "${json_files[@]}"; do
        local parent_dir=""
        local is_nested=false
        local file_display_name
        
        # Determine file type and display name
        if [[ $(dirname "$json_file") != "$lang_dir" ]]; then
            parent_dir=$(basename "$(dirname "$json_file")")
            is_nested=true
            file_display_name="$parent_dir/$(basename "$json_file")"
        else
            file_display_name="$(basename "$json_file")"
        fi
        
        # Find keys to remove from this specific file
        local keys_to_remove=()
        
        for unused_key in "${unused_keys[@]}"; do
            if [[ "$is_nested" == true ]]; then
                # For nested files, check both prefixed and unprefixed versions
                if [[ "$unused_key" == "$parent_dir."* ]]; then
                    local key_without_prefix="${unused_key#$parent_dir.}"
                    if jq -e --arg key "$key_without_prefix" 'has($key)' "$json_file" >/dev/null 2>&1; then
                        keys_to_remove+=("$key_without_prefix")
                    fi
                fi
                # Also check direct match
                if jq -e --arg key "$unused_key" 'has($key)' "$json_file" >/dev/null 2>&1; then
                    keys_to_remove+=("$unused_key")
                fi
            else
                # For root files, check direct key match
                if jq -e --arg key "$unused_key" 'has($key)' "$json_file" >/dev/null 2>&1; then
                    keys_to_remove+=("$unused_key")
                fi
            fi
        done
        
        # Remove all keys from this file in one operation
        if [[ ${#keys_to_remove[@]} -gt 0 ]]; then
            echo "    🗑️  Removing ${#keys_to_remove[@]} keys from $file_display_name"
            
            # Create a single jq command to remove all keys
            local jq_filter="."
            for key in "${keys_to_remove[@]}"; do
                jq_filter="$jq_filter | del(.[\"$key\"])"
            done
            
            local temp_json=$(mktemp)
            jq "$jq_filter" "$json_file" > "$temp_json"
            mv "$temp_json" "$json_file"
            
            total_removed=$((total_removed + ${#keys_to_remove[@]}))
        fi
    done
    
    echo "  ✅ Removed $total_removed unused keys from $lang"
}

# Remove from all languages
for lang in en zh zh-TW; do
    if [[ -d "$LOCALES_DIR/$lang" ]]; then
        remove_keys_from_language "$lang"
        echo
    fi
done

echo "🎉 Batch removal completed!"
echo "🔍 Final verification..."

# Check the final counts
cd "$PROJECT_ROOT"
echo
echo "📊 Final unused key counts:"
./scripts/dev/translation-key-manager.sh unused | grep "Found.*unused keys" | head -3

echo
echo "✅ All unused translation keys have been removed!"
