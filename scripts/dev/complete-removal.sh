#!/bin/bash

# Complete removal of all unused translation keys

set -euo pipefail

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
LOCALES_DIR="$PROJECT_ROOT/frontend/src/utils/localization/locales"

echo "🚀 Complete Removal of ALL Unused Translation Keys"
echo "=================================================="

# Function to remove all unused keys from a specific language
remove_all_unused_from_language() {
    local lang="$1"
    local lang_dir="$LOCALES_DIR/$lang"
    
    echo "🔧 Processing language: $lang"
    
    # Get current unused count
    local initial_count=$(cd "$PROJECT_ROOT" && ./scripts/dev/translation-key-manager.sh unused 2>/dev/null | grep "Found .* unused keys in language: $lang" | grep -o '[0-9]\+' | head -1)
    echo "  📊 Initial unused keys: $initial_count"
    
    if [[ "$initial_count" -eq 0 ]]; then
        echo "  ✅ No unused keys found for $lang"
        return
    fi
    
    # Continue removing until no unused keys remain
    local iteration=1
    local current_count=$initial_count
    
    while [[ "$current_count" -gt 0 ]]; do
        echo "  🔄 Iteration $iteration: Removing unused keys..."
        
        # Run the removal process with timeout
        cd "$PROJECT_ROOT"
        timeout 120 ./scripts/dev/translation-key-manager.sh unused --remove >/dev/null 2>&1 || true
        
        # Check new count
        local new_count=$(./scripts/dev/translation-key-manager.sh unused 2>/dev/null | grep "Found .* unused keys in language: $lang" | grep -o '[0-9]\+' | head -1 || echo "0")
        
        echo "    📉 Reduced from $current_count to $new_count unused keys"
        
        # If no progress was made, break to avoid infinite loop
        if [[ "$new_count" -eq "$current_count" ]]; then
            echo "    ⚠️  No progress made in this iteration, stopping"
            break
        fi
        
        current_count=$new_count
        iteration=$((iteration + 1))
        
        # Safety limit
        if [[ "$iteration" -gt 20 ]]; then
            echo "    ⚠️  Reached iteration limit, stopping"
            break
        fi
    done
    
    echo "  ✅ Completed $lang: $initial_count → $current_count unused keys"
}

# Process each language
for lang in en zh zh-TW; do
    if [[ -d "$LOCALES_DIR/$lang" ]]; then
        remove_all_unused_from_language "$lang"
        echo
    fi
done

echo "🎉 Complete removal process finished!"
echo "🔍 Final verification..."

# Final count check
cd "$PROJECT_ROOT"
echo "📊 Final unused key counts:"
./scripts/dev/translation-key-manager.sh unused | grep "Found.*unused keys"

echo
echo "✅ All unused translation keys removal process completed!"
