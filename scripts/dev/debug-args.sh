#!/bin/bash

# Debug script to test argument parsing

REMOVE_ACTION=false
VERBOSE=false

parse_args() {
    local command=""
    
    echo "DEBUG: Arguments received: $@"
    echo "DEBUG: Number of arguments: $#"
    
    while [[ $# -gt 0 ]]; do
        echo "DEBUG: Processing argument: $1"
        case $1 in
            duplicates|unused|missing|all)
                command="$1"
                echo "DEBUG: Set command to: $command"
                shift
                ;;
            --remove)
                REMOVE_ACTION=true
                echo "DEBUG: Set REMOVE_ACTION to: $REMOVE_ACTION"
                shift
                ;;
            --verbose)
                VERBOSE=true
                echo "DEBUG: Set VERBOSE to: $VERBOSE"
                shift
                ;;
            *)
                echo "DEBUG: Unknown option: $1"
                shift
                ;;
        esac
    done
    
    echo "DEBUG: Final values:"
    echo "DEBUG: command=$command"
    echo "DEBUG: REMOVE_ACTION=$REMOVE_ACTION"
    echo "DEBUG: VERBOSE=$VERBOSE"
    
    echo "$command"
}

# Test the parsing
echo "Testing: unused --remove"
result=$(parse_args unused --remove)
echo "Result: $result"
echo "REMOVE_ACTION after parsing: $REMOVE_ACTION"
