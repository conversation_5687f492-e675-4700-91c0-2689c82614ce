#!/bin/bash

# Emergency Key Synchronization Script
# Fixes all missing keys across all languages by copying from reference languages

set -euo pipefail

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
LOCALES_DIR="$PROJECT_ROOT/frontend/src/utils/localization/locales"

echo "🚨 EMERGENCY KEY SYNCHRONIZATION"
echo "================================"

# Function to sync keys from source to target file
sync_keys() {
    local source_file="$1"
    local target_file="$2"
    local target_lang="$3"
    
    if [[ ! -f "$source_file" ]]; then
        echo "  ⚠️  Source file not found: $source_file"
        return
    fi
    
    # Create target directory if needed
    mkdir -p "$(dirname "$target_file")"
    
    # Create target file if it doesn't exist
    if [[ ! -f "$target_file" ]]; then
        echo "{}" > "$target_file"
    fi
    
    # Get missing keys
    local temp_source=$(mktemp)
    local temp_target=$(mktemp)
    
    jq -r 'keys[]' "$source_file" 2>/dev/null | grep -v "^//" | sort > "$temp_source"
    jq -r 'keys[]' "$target_file" 2>/dev/null | grep -v "^//" | sort > "$temp_target"
    
    local missing_keys=$(comm -23 "$temp_source" "$temp_target")
    
    if [[ -n "$missing_keys" ]]; then
        echo "  ➕ Adding $(echo "$missing_keys" | wc -l) missing keys to $target_lang"
        
        # Add each missing key
        while IFS= read -r key; do
            [[ -z "$key" ]] && continue
            
            # Get the value from source
            local source_value=$(jq -r --arg key "$key" '.[$key]' "$source_file" 2>/dev/null || echo "")
            
            if [[ -n "$source_value" && "$source_value" != "null" ]]; then
                # Create appropriate value for target language
                local target_value
                if [[ "$target_lang" == "en" ]]; then
                    # For English, create readable value from key
                    target_value=$(echo "$key" | sed 's/\./\ /g' | sed 's/\${[^}]*}/[dynamic]/g')
                else
                    # For other languages, use translation placeholder
                    target_value="[TODO: Translate] $key"
                fi
                
                # Add the key using jq
                local temp_json=$(mktemp)
                if jq --arg key "$key" --arg value "$target_value" '. + {($key): $value}' "$target_file" > "$temp_json" 2>/dev/null; then
                    mv "$temp_json" "$target_file"
                    echo "    ✅ Added: $key"
                else
                    echo "    ❌ Failed to add: $key"
                    rm -f "$temp_json"
                fi
            fi
        done <<< "$missing_keys"
    fi
    
    rm -f "$temp_source" "$temp_target"
}

# Function to sync a specific file across all languages
sync_file_across_languages() {
    local file_path="$1"  # e.g., "admin/applications.json"
    
    echo "📄 Syncing: $file_path"
    
    local en_file="$LOCALES_DIR/en/$file_path"
    local zh_file="$LOCALES_DIR/zh/$file_path"
    local zh_tw_file="$LOCALES_DIR/zh-TW/$file_path"
    
    # Use Chinese as reference (it seems to have the most complete keys)
    # Sync missing keys to English
    if [[ -f "$zh_file" ]]; then
        sync_keys "$zh_file" "$en_file" "en"
    fi
    
    # Sync missing keys to Traditional Chinese
    if [[ -f "$zh_file" ]]; then
        sync_keys "$zh_file" "$zh_tw_file" "zh-TW"
    fi
    
    # Also sync any keys that might be only in English to other languages
    if [[ -f "$en_file" ]]; then
        sync_keys "$en_file" "$zh_file" "zh"
        sync_keys "$en_file" "$zh_tw_file" "zh-TW"
    fi
}

# Get all unique JSON files
echo "🔍 Discovering all translation files..."
all_files=$(find "$LOCALES_DIR" -name "*.json" -type f | sed "s|$LOCALES_DIR/[^/]*/||" | sort -u)

echo "📊 Found $(echo "$all_files" | wc -l) unique translation files"
echo

# Sync each file
while IFS= read -r file; do
    sync_file_across_languages "$file"
done <<< "$all_files"

echo
echo "🔍 Verification after sync..."
echo "============================="

# Run verification again
missing_count=$(./scripts/dev/verify-all-keys.sh 2>/dev/null | grep -E "(Missing in EN|Missing in ZH|Missing in ZH-TW)" | wc -l || echo "0")

echo "📊 Files with missing keys after sync: $missing_count"

if [[ "$missing_count" -eq 0 ]]; then
    echo "🎉 SUCCESS: All keys are now synchronized across all languages!"
else
    echo "⚠️  Some keys may still be missing. Check the verification output above."
fi

echo
echo "✅ Emergency key synchronization completed!"
