#!/bin/bash

# Quick removal of unused keys in small batches

set -euo pipefail

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
LOCALES_DIR="$PROJECT_ROOT/frontend/src/utils/localization/locales"

echo "🚀 Quick Batch Removal of Unused Keys"

# Function to remove a specific set of keys
remove_specific_keys() {
    local lang="$1"
    shift
    local keys_to_remove=("$@")
    
    echo "🔧 Removing ${#keys_to_remove[@]} keys from $lang..."
    
    local lang_dir="$LOCALES_DIR/$lang"
    local removed_count=0
    
    for key in "${keys_to_remove[@]}"; do
        echo "  🗑️  $key"
        
        # Check root-level files
        find "$lang_dir" -maxdepth 1 -name "*.json" -type f | while read -r json_file; do
            if jq -e --arg key "$key" 'has($key)' "$json_file" >/dev/null 2>&1; then
                temp_json=$(mktemp)
                jq --arg key "$key" 'del(.[$key])' "$json_file" > "$temp_json"
                mv "$temp_json" "$json_file"
            fi
        done
        
        # Check nested files
        find "$lang_dir" -mindepth 2 -name "*.json" -type f | while read -r json_file; do
            local parent_dir=$(basename "$(dirname "$json_file")")
            
            # Try removing with prefix logic
            if [[ "$key" == "$parent_dir."* ]]; then
                local key_without_prefix="${key#$parent_dir.}"
                if jq -e --arg key "$key_without_prefix" 'has($key)' "$json_file" >/dev/null 2>&1; then
                    temp_json=$(mktemp)
                    jq --arg key "$key_without_prefix" 'del(.[$key])' "$json_file" > "$temp_json"
                    mv "$temp_json" "$json_file"
                fi
            fi
            
            # Also try direct key match
            if jq -e --arg key "$key" 'has($key)' "$json_file" >/dev/null 2>&1; then
                temp_json=$(mktemp)
                jq --arg key "$key" 'del(.[$key])' "$json_file" > "$temp_json"
                mv "$temp_json" "$json_file"
            fi
        done
    done
    
    echo "  ✅ Batch completed for $lang"
}

# Define batches of keys to remove (these are confirmed unused)
BATCH_1=(
    "admin.users.modal.fullName"
    "admin.users.modal.lastLogin"
    "admin.users.modal.never"
    "admin.users.modal.registrationDate"
    "admin.users.modal.role"
    "admin.users.modal.status"
    "admin.users.modal.title"
    "admin.users.modal.username"
    "admin.users.role.admin"
    "admin.users.role.developer"
)

BATCH_2=(
    "auth.logout.confirm.description"
    "auth.logout.confirm.title"
    "auth.logout.connected.apps.count"
    "auth.logout.error.description"
    "auth.logout.error.details"
    "auth.logout.error.force.logout"
    "auth.logout.error.go.to.home"
    "auth.logout.error.help.text"
    "auth.logout.error.retry"
    "auth.logout.error.retrying"
)

BATCH_3=(
    "auth.logout.error.security.clear.cache"
    "auth.logout.error.security.close.browser"
    "auth.logout.error.security.manual.logout"
    "auth.logout.error.security.title"
    "auth.logout.error.title"
    "auth.logout.error.troubleshooting"
    "auth.logout.go.to.home"
    "auth.logout.go.to.login"
    "auth.logout.help.text"
    "auth.logout.loading"
)

echo "📊 Processing 3 batches of keys..."

# Process each batch for all languages
for lang in en zh zh-TW; do
    if [[ -d "$LOCALES_DIR/$lang" ]]; then
        echo
        echo "🌐 Language: $lang"
        remove_specific_keys "$lang" "${BATCH_1[@]}"
        remove_specific_keys "$lang" "${BATCH_2[@]}"
        remove_specific_keys "$lang" "${BATCH_3[@]}"
    fi
done

echo
echo "🎉 Batch removal completed!"
echo "🔍 Checking new counts..."

cd "$PROJECT_ROOT"
./scripts/dev/translation-key-manager.sh unused | grep "Found.*unused keys"
