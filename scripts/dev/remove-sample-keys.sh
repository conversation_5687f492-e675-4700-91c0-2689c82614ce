#!/bin/bash

# Simple script to remove a few sample unused keys to test the removal process

set -euo pipefail

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
LOCALES_DIR="$PROJECT_ROOT/frontend/src/utils/localization/locales"

echo "🧹 Testing removal of sample unused keys"

# Sample keys to remove (these are confirmed unused)
SAMPLE_KEYS=(
    "account"
    "activate" 
    "activity"
    "address"
    "admin.applications.actions.activateApplication"
)

echo "📋 Keys to remove: ${SAMPLE_KEYS[@]}"

# Function to remove keys from a specific language
remove_keys_from_language() {
    local lang="$1"
    local lang_dir="$LOCALES_DIR/$lang"
    
    echo "🔧 Processing language: $lang"
    
    for key in "${SAMPLE_KEYS[@]}"; do
        echo "  🗑️  Removing key: $key"
        
        # Check root-level files
        find "$lang_dir" -maxdepth 1 -name "*.json" -type f | while read -r json_file; do
            if jq -e --arg key "$key" 'has($key)' "$json_file" >/dev/null 2>&1; then
                echo "    ✅ Found in $(basename "$json_file"), removing..."
                local temp_json=$(mktemp)
                jq --arg key "$key" 'del(.[$key])' "$json_file" > "$temp_json"
                mv "$temp_json" "$json_file"
            fi
        done
        
        # Check nested files
        find "$lang_dir" -mindepth 2 -name "*.json" -type f | while read -r json_file; do
            local parent_dir=$(basename "$(dirname "$json_file")")
            
            # Try removing with prefix logic
            if [[ "$key" == "$parent_dir."* ]]; then
                local key_without_prefix="${key#$parent_dir.}"
                if jq -e --arg key "$key_without_prefix" 'has($key)' "$json_file" >/dev/null 2>&1; then
                    echo "    ✅ Found '$key_without_prefix' in $parent_dir/$(basename "$json_file"), removing..."
                    local temp_json=$(mktemp)
                    jq --arg key "$key_without_prefix" 'del(.[$key])' "$json_file" > "$temp_json"
                    mv "$temp_json" "$json_file"
                fi
            fi
            
            # Also try direct key match
            if jq -e --arg key "$key" 'has($key)' "$json_file" >/dev/null 2>&1; then
                echo "    ✅ Found '$key' in $parent_dir/$(basename "$json_file"), removing..."
                local temp_json=$(mktemp)
                jq --arg key "$key" 'del(.[$key])' "$json_file" > "$temp_json"
                mv "$temp_json" "$json_file"
            fi
        done
    done
}

# Remove from all languages
for lang in en zh zh-TW; do
    if [[ -d "$LOCALES_DIR/$lang" ]]; then
        remove_keys_from_language "$lang"
    fi
done

echo "✅ Sample key removal completed!"
echo "🔍 Verifying removal by checking unused keys count..."

# Check the new count
cd "$PROJECT_ROOT"
./scripts/dev/translation-key-manager.sh unused | grep "Found.*unused keys" | head -3
