#!/bin/bash

# Remove unused keys from a single language efficiently

set -euo pipefail

if [[ $# -ne 1 ]]; then
    echo "Usage: $0 <language>"
    echo "Example: $0 en"
    exit 1
fi

LANG_CODE="$1"
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
LOCALES_DIR="$PROJECT_ROOT/frontend/src/utils/localization/locales/$LANG_CODE"

if [[ ! -d "$LOCALES_DIR" ]]; then
    echo "❌ Language directory not found: $LOCALES_DIR"
    exit 1
fi

echo "🧹 Removing unused keys from language: $LANG_CODE"

# Get unused keys for this language
echo "📊 Getting unused keys..."
cd "$PROJECT_ROOT"
UNUSED_KEYS_FILE=$(mktemp)
./scripts/dev/translation-key-manager.sh unused 2>/dev/null | \
    grep -A 10000 "Found .* unused keys in language: $LANG_CODE" | \
    grep "^\\[WARNING\\]   - " | \
    sed 's/^\\[WARNING\\]   - //' > "$UNUSED_KEYS_FILE"

# Remove the last line if it contains "... and X more"
sed -i '' '/\.\.\. and .* more/d' "$UNUSED_KEYS_FILE"

TOTAL_KEYS=$(wc -l < "$UNUSED_KEYS_FILE" | tr -d ' ')
echo "📋 Found $TOTAL_KEYS unused keys to remove"

if [[ "$TOTAL_KEYS" -eq 0 ]]; then
    echo "✅ No unused keys found for $LANG_CODE"
    rm "$UNUSED_KEYS_FILE"
    exit 0
fi

# Process each JSON file
REMOVED_COUNT=0
while IFS= read -r -d '' json_file; do
    echo "🔧 Processing $(basename "$(dirname "$json_file")")/$(basename "$json_file")..."
    
    parent_dir=""
    is_nested=false
    
    # Check if this is a nested file
    if [[ $(dirname "$json_file") != "$LOCALES_DIR" ]]; then
        parent_dir=$(basename "$(dirname "$json_file")")
        is_nested=true
    fi
    
    # Build jq filter to remove all unused keys from this file
    jq_filter="."
    keys_removed_from_file=0
    
    while IFS= read -r unused_key; do
        [[ -z "$unused_key" ]] && continue
        
        key_exists=false
        key_to_remove=""
        
        if [[ "$is_nested" == true ]]; then
            # For nested files, check both prefixed and unprefixed versions
            if [[ "$unused_key" == "$parent_dir."* ]]; then
                key_without_prefix="${unused_key#$parent_dir.}"
                if jq -e --arg key "$key_without_prefix" 'has($key)' "$json_file" >/dev/null 2>&1; then
                    key_exists=true
                    key_to_remove="$key_without_prefix"
                fi
            fi
            
            # Also check direct match
            if [[ "$key_exists" == false ]] && jq -e --arg key "$unused_key" 'has($key)' "$json_file" >/dev/null 2>&1; then
                key_exists=true
                key_to_remove="$unused_key"
            fi
        else
            # For root files, check direct key match
            if jq -e --arg key "$unused_key" 'has($key)' "$json_file" >/dev/null 2>&1; then
                key_exists=true
                key_to_remove="$unused_key"
            fi
        fi
        
        if [[ "$key_exists" == true ]]; then
            jq_filter="$jq_filter | del(.[\"$key_to_remove\"])"
            keys_removed_from_file=$((keys_removed_from_file + 1))
        fi
    done < "$UNUSED_KEYS_FILE"
    
    # Apply the removal if any keys were found
    if [[ "$keys_removed_from_file" -gt 0 ]]; then
        echo "  🗑️  Removing $keys_removed_from_file keys..."
        temp_json=$(mktemp)
        jq "$jq_filter" "$json_file" > "$temp_json"
        mv "$temp_json" "$json_file"
        REMOVED_COUNT=$((REMOVED_COUNT + keys_removed_from_file))
    fi
    
done < <(find "$LOCALES_DIR" -name "*.json" -type f -print0)

echo "✅ Removed $REMOVED_COUNT unused keys from $LANG_CODE"

# Cleanup
rm "$UNUSED_KEYS_FILE"

# Verify the result
echo "🔍 Verifying removal..."
NEW_COUNT=$(./scripts/dev/translation-key-manager.sh unused 2>/dev/null | grep "Found .* unused keys in language: $LANG_CODE" | grep -o '[0-9]\+' | head -1)
echo "📊 Unused keys remaining in $LANG_CODE: $NEW_COUNT"

echo "🎉 Removal completed for $LANG_CODE!"
