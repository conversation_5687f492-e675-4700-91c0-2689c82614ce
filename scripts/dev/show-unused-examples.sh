#!/bin/bash

# Show examples of unused translation keys

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
FRONTEND_DIR="$PROJECT_ROOT/frontend"
LOCALES_DIR="$FRONTEND_DIR/src/utils/localization/locales"
COMPONENTS_DIR="$FRONTEND_DIR/src/components"
PAGES_DIR="$FRONTEND_DIR/src/pages"

echo "🗑️ UNUSED Translation Keys Examples"
echo "===================================="

# Get used keys
echo "Getting used keys..."
used_keys_file=$(mktemp)
find "$COMPONENTS_DIR" "$PAGES_DIR" -type f \( -name "*.tsx" -o -name "*.ts" \) -print0 | \
    xargs -0 grep -oE "intl\.formatMessage\(\{\s*id:\s*['\"][^'\"]+['\"]" 2>/dev/null | \
    grep -oE "['\"][^'\"]+['\"]" | \
    tr -d "\"'" | \
    sort -u > "$used_keys_file"

# Get available keys from English
echo "Getting available keys..."
available_keys_file=$(mktemp)
find "$LOCALES_DIR/en" -name "*.json" -type f -exec jq -r 'keys[]' {} \; 2>/dev/null | \
    grep -v '^$' | grep -v '^//' | sort -u > "$available_keys_file"

# Find unused keys
unused_keys_file=$(mktemp)
comm -23 "$available_keys_file" "$used_keys_file" > "$unused_keys_file"

used_count=$(wc -l < "$used_keys_file")
available_count=$(wc -l < "$available_keys_file")
unused_count=$(wc -l < "$unused_keys_file")

echo ""
echo "📊 Statistics:"
echo "  Used in code: $used_count keys"
echo "  Available: $available_count keys"
echo "  Unused: $unused_count keys"

echo ""
echo "🗑️ Examples of UNUSED keys by category:"

echo ""
echo "📝 Navigation keys (nav.*):"
grep "^nav\." "$unused_keys_file" | head -5 | while read -r key; do
    echo "  - $key"
done

echo ""
echo "🔧 Admin action keys (admin.*.actions.*):"
grep "admin\..*\.actions\." "$unused_keys_file" | head -5 | while read -r key; do
    echo "  - $key"
done

echo ""
echo "📄 Page keys (page.*):"
grep "^page\." "$unused_keys_file" | head -5 | while read -r key; do
    echo "  - $key"
done

echo ""
echo "👤 User keys (user.*):"
grep "^user\." "$unused_keys_file" | head -5 | while read -r key; do
    echo "  - $key"
done

echo ""
echo "🏢 Developer keys (developer.*):"
grep "^developer\." "$unused_keys_file" | head -5 | while read -r key; do
    echo "  - $key"
done

echo ""
echo "🎨 UI/Component keys:"
grep -E "^(ui\.|component\.|button\.|form\.|table\.|modal\.)" "$unused_keys_file" | head -5 | while read -r key; do
    echo "  - $key"
done

echo ""
echo "🔤 Simple/Generic keys:"
grep -E "^[a-z]+$" "$unused_keys_file" | head -10 | while read -r key; do
    echo "  - $key"
done

# Cleanup
rm -f "$used_keys_file" "$available_keys_file" "$unused_keys_file"

echo ""
echo "===================================="
echo "✅ These are examples of keys that exist in translation files"
echo "   but are NOT used anywhere in the source code."
echo ""
echo "💡 To see ALL unused keys, run:"
echo "   ./scripts/dev/translation-key-manager.sh unused --verbose"
