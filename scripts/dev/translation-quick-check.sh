#!/bin/bash

# Quick Translation Check
# Fast overview without complex calculations

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
FRONTEND_DIR="$PROJECT_ROOT/frontend"
LOCALES_DIR="$FRONTEND_DIR/src/utils/localization/locales"
COMPONENTS_DIR="$FRONTEND_DIR/src/components"
PAGES_DIR="$FRONTEND_DIR/src/pages"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }

echo "🔍 Quick Translation System Check"
echo "=================================="

# Count used keys in source code
log_info "Counting translation keys used in source code..."
USED_KEYS_COUNT=$(find "$COMPONENTS_DIR" "$PAGES_DIR" -type f \( -name "*.tsx" -o -name "*.ts" -o -name "*.jsx" -o -name "*.js" \) -print0 | \
    xargs -0 grep -oE "intl\.formatMessage\(\{\s*id:\s*['\"][^'\"]+['\"]" 2>/dev/null | \
    grep -oE "['\"][^'\"]+['\"]" | \
    tr -d "\"'" | \
    sort -u | wc -l)

echo "📊 Used in code: $USED_KEYS_COUNT keys"
echo ""

# Check each language
for lang_dir in "$LOCALES_DIR"/*; do
    if [[ -d "$lang_dir" ]]; then
        lang=$(basename "$lang_dir")
        
        # Count available keys (excluding comments)
        available_count=$(find "$lang_dir" -name "*.json" -type f -exec jq -r 'keys[]' {} \; 2>/dev/null | \
            grep -v '^$' | grep -v '^//' | sort -u | wc -l)
        
        echo "🌐 Language: $lang"
        echo "   📚 Available keys: $available_count"
        
        # Simple status
        if [[ $available_count -ge $USED_KEYS_COUNT ]]; then
            log_success "   ✅ Status: Likely complete (has more keys than used)"
        else
            log_warning "   ⚠️  Status: Missing keys (has fewer keys than used)"
        fi
        echo ""
    fi
done

echo "=================================="
log_info "For detailed analysis, run:"
echo "  ./scripts/dev/translation-key-manager.sh missing"
echo "  ./scripts/dev/translation-key-manager.sh duplicates"
echo ""
log_info "Key examples that might be missing:"

# Show some example keys that might be missing
find "$COMPONENTS_DIR" "$PAGES_DIR" -type f \( -name "*.tsx" -o -name "*.ts" -o -name "*.jsx" -o -name "*.js" \) -print0 | \
    xargs -0 grep -oE "intl\.formatMessage\(\{\s*id:\s*['\"][^'\"]+['\"]" 2>/dev/null | \
    grep -oE "['\"][^'\"]+['\"]" | \
    tr -d "\"'" | \
    grep -E "(page\.landing\.stats|auth\.|developer\.organizations)" | \
    head -5 | while read -r key; do
        log_warning "  - $key"
    done

echo ""
log_success "Quick check completed!"
