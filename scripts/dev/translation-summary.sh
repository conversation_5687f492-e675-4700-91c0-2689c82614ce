#!/bin/bash

# Translation Summary Script
# Quick overview of translation system status

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
FRONTEND_DIR="$PROJECT_ROOT/frontend"
LOCALES_DIR="$FRONTEND_DIR/src/utils/localization/locales"
COMPONENTS_DIR="$FRONTEND_DIR/src/components"
PAGES_DIR="$FRONTEND_DIR/src/pages"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Get all supported languages
get_languages() {
    find "$LOCALES_DIR" -maxdepth 1 -type d -not -path "$LOCALES_DIR" -exec basename {} \; | sort
}

# Get all translation keys used in source files
get_used_keys() {
    find "$COMPONENTS_DIR" "$PAGES_DIR" -type f \( -name "*.tsx" -o -name "*.ts" -o -name "*.jsx" -o -name "*.js" \) -print0 | \
        xargs -0 grep -oE "intl\.formatMessage\(\{\s*id:\s*['\"][^'\"]+['\"]" 2>/dev/null | \
        grep -oE "['\"][^'\"]+['\"]" | \
        tr -d "\"'" | \
        sort -u || true
}

# Get all available translation keys for a language
get_available_keys() {
    local lang="$1"
    
    # Get keys from root-level JSON files
    find "$LOCALES_DIR/$lang" -maxdepth 1 -name "*.json" -type f | while read -r json_file; do
        jq -r 'keys[]' "$json_file" 2>/dev/null | grep -v '^$' | grep -v '^//' || true
    done
    
    # Get keys from nested subcategory files (keys already have prefixes)
    find "$LOCALES_DIR/$lang" -mindepth 2 -name "*.json" -type f | while read -r json_file; do
        jq -r 'keys[]' "$json_file" 2>/dev/null | grep -v '^$' | grep -v '^//' || true
    done
}

# Count missing keys for a language
count_missing_keys() {
    local lang="$1"
    local used_keys=()
    while IFS= read -r key; do
        [[ -n "$key" ]] && used_keys+=("$key")
    done < <(get_used_keys)
    
    local available_keys=()
    while IFS= read -r key; do
        [[ -n "$key" ]] && available_keys+=("$key")
    done < <(get_available_keys "$lang")
    
    local missing_count=0
    for used_key in "${used_keys[@]}"; do
        local found=false
        for available_key in "${available_keys[@]}"; do
            if [[ "$available_key" == "$used_key" ]]; then
                found=true
                break
            fi
        done
        if [[ "$found" == false ]]; then
            ((missing_count++))
        fi
    done
    
    echo "$missing_count"
}

# Count available keys for a language
count_available_keys() {
    local lang="$1"
    get_available_keys "$lang" | wc -l
}

# Main summary
main() {
    log_info "Translation System Summary"
    echo "=================================="
    
    # Get used keys count
    local used_keys_count
    used_keys_count=$(get_used_keys | wc -l)
    log_info "Total translation keys used in code: $used_keys_count"
    
    echo ""
    log_info "Language Status:"
    
    local languages=()
    while IFS= read -r lang; do
        languages+=("$lang")
    done < <(get_languages)
    
    for lang in "${languages[@]}"; do
        local available_count
        available_count=$(count_available_keys "$lang")
        local missing_count
        missing_count=$(count_missing_keys "$lang")
        local unused_count=$((available_count - used_keys_count + missing_count))
        
        echo "  Language: $lang"
        echo "    Available keys: $available_count"
        echo "    Missing keys: $missing_count"
        echo "    Estimated unused keys: $unused_count"
        
        if [[ $missing_count -gt 0 ]]; then
            log_warning "    Status: Missing translations"
        else
            log_success "    Status: Complete"
        fi
        echo ""
    done
    
    echo "=================================="
    log_info "Key Examples Missing in ALL Languages:"
    
    # Show some missing key examples
    get_used_keys | head -10 | while read -r key; do
        local missing_in_all=true
        for lang in "${languages[@]}"; do
            if get_available_keys "$lang" | grep -q "^$key$"; then
                missing_in_all=false
                break
            fi
        done
        
        if [[ "$missing_in_all" == true ]]; then
            log_warning "  - $key"
        fi
    done
    
    echo ""
    log_info "To see detailed analysis, run:"
    log_info "  ./scripts/dev/translation-key-manager.sh missing"
    log_info "  ./scripts/dev/translation-key-manager.sh duplicates"
    log_info "  ./scripts/dev/translation-key-manager.sh unused"
}

# Check dependencies
if ! command -v jq &> /dev/null; then
    log_error "jq is required but not installed. Please install it:"
    log_info "  macOS: brew install jq"
    log_info "  Ubuntu: apt-get install jq"
    exit 1
fi

main "$@"
