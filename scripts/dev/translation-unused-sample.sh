#!/bin/bash

# Fast Unused Translation Keys Sample
# Shows examples of unused keys without full analysis

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
FRONTEND_DIR="$PROJECT_ROOT/frontend"
LOCALES_DIR="$FRONTEND_DIR/src/utils/localization/locales"
COMPONENTS_DIR="$FRONTEND_DIR/src/components"
PAGES_DIR="$FRONTEND_DIR/src/pages"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }

echo "🔍 Unused Translation Keys Sample"
echo "=================================="

# Get used keys
log_info "Getting used translation keys..."
used_keys_file=$(mktemp)
find "$COMPONENTS_DIR" "$PAGES_DIR" -type f \( -name "*.tsx" -o -name "*.ts" -o -name "*.jsx" -o -name "*.js" \) -print0 | \
    xargs -0 grep -oE "intl\.formatMessage\(\{\s*id:\s*['\"][^'\"]+['\"]" 2>/dev/null | \
    grep -oE "['\"][^'\"]+['\"]" | \
    tr -d "\"'" | \
    sort -u > "$used_keys_file"

used_count=$(wc -l < "$used_keys_file")
log_info "Found $used_count used keys in source code"

# Check each language
for lang_dir in "$LOCALES_DIR"/*; do
    if [[ -d "$lang_dir" ]]; then
        lang=$(basename "$lang_dir")
        echo ""
        echo "🌐 Language: $lang"
        
        # Get available keys
        available_keys_file=$(mktemp)
        find "$lang_dir" -name "*.json" -type f -exec jq -r 'keys[]' {} \; 2>/dev/null | \
            grep -v '^$' | grep -v '^//' | sort -u > "$available_keys_file"
        
        available_count=$(wc -l < "$available_keys_file")
        echo "   📚 Available keys: $available_count"
        
        # Find unused keys (sample)
        unused_keys_file=$(mktemp)
        comm -23 "$available_keys_file" "$used_keys_file" > "$unused_keys_file"
        unused_count=$(wc -l < "$unused_keys_file")
        
        if [[ $unused_count -gt 0 ]]; then
            log_warning "   🗑️  Unused keys: $unused_count"
            echo ""
            log_warning "   Examples of unused keys:"
            
            # Show examples by category
            echo "   📝 Navigation/UI examples:"
            grep -E "^(nav\.|ui\.|button\.|menu\.)" "$unused_keys_file" | head -3 | while read -r key; do
                echo "      - $key"
            done
            
            echo "   📄 Page-specific examples:"
            grep -E "^(page\.|admin\.|developer\.|user\.)" "$unused_keys_file" | head -3 | while read -r key; do
                echo "      - $key"
            done
            
            echo "   🔧 Component examples:"
            grep -E "^(component\.|form\.|table\.|modal\.)" "$unused_keys_file" | head -3 | while read -r key; do
                echo "      - $key"
            done
            
            echo "   📊 Other examples:"
            grep -vE "^(nav\.|ui\.|button\.|menu\.|page\.|admin\.|developer\.|user\.|component\.|form\.|table\.|modal\.)" "$unused_keys_file" | head -5 | while read -r key; do
                echo "      - $key"
            done
            
            if [[ $unused_count -gt 20 ]]; then
                echo "      ... and $((unused_count - 20)) more unused keys"
            fi
        else
            echo "   ✅ No unused keys found"
        fi
        
        # Cleanup
        rm -f "$available_keys_file" "$unused_keys_file"
    fi
done

# Cleanup
rm -f "$used_keys_file"

echo ""
echo "=================================="
log_info "For complete analysis, run:"
echo "  ./scripts/dev/translation-key-manager.sh unused --verbose"
echo ""
log_info "Sample completed!"
