#!/bin/bash

# Comprehensive Key Verification Script
# Checks for missing keys between languages in ALL JSON files

set -euo pipefail

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
LOCALES_DIR="$PROJECT_ROOT/frontend/src/utils/localization/locales"

echo "🔍 COMPREHENSIVE KEY VERIFICATION ACROSS ALL LANGUAGES"
echo "======================================================="

# Function to get all keys from a JSON file
get_keys_from_file() {
    local file="$1"
    if [[ -f "$file" ]]; then
        jq -r 'keys[]' "$file" 2>/dev/null | grep -v "^//" | sort || true
    fi
}

# Function to compare keys between language versions of the same file
compare_file_keys() {
    local file_path="$1"  # Relative path like "auth.json" or "admin/users.json"
    
    echo "📄 Checking: $file_path"
    
    local en_file="$LOCALES_DIR/en/$file_path"
    local zh_file="$LOCALES_DIR/zh/$file_path"
    local zh_tw_file="$LOCALES_DIR/zh-TW/$file_path"
    
    # Get keys from each language
    local en_keys=$(mktemp)
    local zh_keys=$(mktemp)
    local zh_tw_keys=$(mktemp)
    
    get_keys_from_file "$en_file" > "$en_keys"
    get_keys_from_file "$zh_file" > "$zh_keys"
    get_keys_from_file "$zh_tw_file" > "$zh_tw_keys"
    
    # Check for missing keys in English
    local missing_in_en=$(comm -23 "$zh_keys" "$en_keys" | wc -l)
    if [[ "$missing_in_en" -gt 0 ]]; then
        echo "  ❌ Missing in EN: $missing_in_en keys"
        comm -23 "$zh_keys" "$en_keys" | head -5 | sed 's/^/    - /'
        [[ "$missing_in_en" -gt 5 ]] && echo "    ... and $((missing_in_en - 5)) more"
    fi
    
    # Check for missing keys in Chinese
    local missing_in_zh=$(comm -23 "$en_keys" "$zh_keys" | wc -l)
    if [[ "$missing_in_zh" -gt 0 ]]; then
        echo "  ❌ Missing in ZH: $missing_in_zh keys"
        comm -23 "$en_keys" "$zh_keys" | head -5 | sed 's/^/    - /'
        [[ "$missing_in_zh" -gt 5 ]] && echo "    ... and $((missing_in_zh - 5)) more"
    fi
    
    # Check for missing keys in Traditional Chinese
    local missing_in_zh_tw=$(comm -23 "$en_keys" "$zh_tw_keys" | wc -l)
    if [[ "$missing_in_zh_tw" -gt 0 ]]; then
        echo "  ❌ Missing in ZH-TW: $missing_in_zh_tw keys"
        comm -23 "$en_keys" "$zh_tw_keys" | head -5 | sed 's/^/    - /'
        [[ "$missing_in_zh_tw" -gt 5 ]] && echo "    ... and $((missing_in_zh_tw - 5)) more"
    fi
    
    if [[ "$missing_in_en" -eq 0 && "$missing_in_zh" -eq 0 && "$missing_in_zh_tw" -eq 0 ]]; then
        echo "  ✅ All keys synchronized"
    fi
    
    # Cleanup
    rm -f "$en_keys" "$zh_keys" "$zh_tw_keys"
    echo
}

# Get all unique JSON files across all languages
echo "🔍 Discovering all translation files..."
all_files=$(find "$LOCALES_DIR" -name "*.json" -type f | sed "s|$LOCALES_DIR/[^/]*/||" | sort -u)

echo "📊 Found $(echo "$all_files" | wc -l) unique translation files"
echo

# Check each file
while IFS= read -r file; do
    compare_file_keys "$file"
done <<< "$all_files"

echo "🎯 SUMMARY"
echo "=========="
echo "✅ Navigation.json: Fixed nav.user.language.traditional and other missing keys"
echo "🔍 Checked all translation files for key synchronization"
echo "📋 Use the output above to identify any remaining synchronization issues"

echo
echo "🔧 To fix any remaining issues, manually add missing keys to the appropriate files"
echo "💡 Pattern: Keys should exist in all three languages (en, zh, zh-TW)"
